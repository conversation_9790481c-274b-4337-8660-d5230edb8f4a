version: '3'
services:

  worklink-service:
    build:
      context: ./worklink-service
      dockerfile: Dockerfile
    image: tinashemk/myrepository:worklink-service
    container_name: worklink-service
    restart: unless-stopped
    environment:
      - "SPRING_PROFILES_ACTIVE=dev"
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - PAYNOWINTEGRATIONKEY=${PAYNOWINTEGRATIONKEY}
      - PAYNOWINTEGRATIONID=${PAYNOWINTEGRATIONID}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
      - STRIPE_PUBLIC_KEY=${STRIPE_PUBLIC_KEY}
    ports:
      - '8300:8300'
    network_mode: host

  forms-service:
    build:
      context: ./forms-service
      dockerfile: Dockerfile
    image: tinashemk/myrepository:forms-service
    container_name: forms-service
    restart: unless-stopped
    environment:
      - "SPRING_PROFILES_ACTIVE=dev"
    ports:
      - '8307:8307'
    network_mode: host


  oauth-service:
    build:
      context: ./ouath-service
      dockerfile: Dockerfile
    image: tinashemk/myrepository:oauth-service
    environment:
      - "SPRING_PROFILES_ACTIVE=dev"
    container_name: oauth-service
    ports:
      - '8203:8203'
    restart: unless-stopped
    network_mode: host

  eureka-service:
    build:
      context: ./eureka-service
      dockerfile: Dockerfile
    image: tinashemk/myrepository:eureka-service
    environment:
      - "SPRING_PROFILES_ACTIVE=dev"
    container_name: eurekaserver
    ports:
      - '8761:8761'
    restart: unless-stopped
    network_mode: host


  user-service:
    build:
      context: ./user-service
      dockerfile: Dockerfile
    image: tinashemk/myrepository:user-service
    environment:
      - "SPRING_PROFILES_ACTIVE=dev"
    container_name: user-service
    ports:
      - '8204:8204'
    restart: unless-stopped
    network_mode: host

  api-gateway:
    build:
      context: ./zuul-service
      dockerfile: Dockerfile
    image: tinashemk/myrepository:api-gateway
    container_name: api-gateway
    restart: unless-stopped
    environment:
      - "SPRING_PROFILES_ACTIVE=dev"
    ports:
      - '8765:8765'
    network_mode: host 

volumes:
  logs: