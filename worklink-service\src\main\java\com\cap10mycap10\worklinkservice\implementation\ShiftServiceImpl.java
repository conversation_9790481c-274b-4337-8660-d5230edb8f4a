package com.cap10mycap10.worklinkservice.implementation;


import com.cap10mycap10.worklinkservice.dao.ShiftRepository;
import com.cap10mycap10.worklinkservice.dao.WorkerAppliedShiftRepository;
import com.cap10mycap10.worklinkservice.dao.WorkerRepository;
import com.cap10mycap10.worklinkservice.dto.notification.NotificationCreateDto;
import com.cap10mycap10.worklinkservice.dto.shift.*;
import com.cap10mycap10.worklinkservice.dto.worker.WorkerShiftDTO;
import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.cap10mycap10.worklinkservice.enums.WorkerTrainingSessionStatus;
import com.cap10mycap10.worklinkservice.events.email.EmailService;
import com.cap10mycap10.worklinkservice.helpers.PostCodeMapper;
import com.cap10mycap10.worklinkservice.helpers.PushNotification;
import com.cap10mycap10.worklinkservice.mapper.shift.ShiftDtoToShift;
import com.cap10mycap10.worklinkservice.mapper.shift.ShiftToShiftResultDto;
import com.cap10mycap10.worklinkservice.mapper.shift.ShiftUpdateDtoToShift;
import com.cap10mycap10.worklinkservice.mapper.worker.WorkerToWorkerResultDto;
import com.cap10mycap10.worklinkservice.model.*;
import com.cap10mycap10.worklinkservice.service.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.firebase.messaging.FirebaseMessagingException;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.internal.util.stereotypes.Lazy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import springfox.documentation.swagger2.mappers.ModelMapper;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static java.time.LocalDateTime.now;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;


@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ShiftServiceImpl implements ShiftService {

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private TransportBookingService transportBookingService;

    @Autowired
    private TransportService transportService;

    @Autowired
    private ShiftDirectorateService shiftDirectorateService;

    @Autowired
    private ModelMapper modelMapper;

    @Autowired
    private WorkerTrainingSessionService workerTrainingSessionService;

    @Autowired
    private ShiftTypeService shiftTypeService;

    @Autowired
    private AssignmentCodeService assignmentCodeService;

    private final ShiftRepository shiftRepository;

    @Lazy
    private final WorkerService workerService;
    @Autowired
    private ShiftToShiftResultDto toShiftResultDto;

    private final WorkerRepository workerRepository;

    private final DeviceService deviceService;

    private final NotificationService notificationService;

    private final PushNotification pushNotification;
    private final ShiftUpdateDtoToShift toUpdateShift;
    private final ShiftDtoToShift toShift;
    private final AgencyService agencyService;
    @Lazy
    private final ChatGroupService chatGroupService;

    @Lazy
    private final ClientService clientService;
    private final EmailService emailService;
    private final WorkerAppliedShiftRepository repository;
    private final WorkerToWorkerResultDto toWorkerResultDto;

    private final WorkerAppliedShiftRepository workerAppliedShiftRepository;

    @Value("${iosApp.url}")
    private String iosAppUrl;

    @Value("${androidApp.url}")
    private String androidAppUrl;

    public ShiftServiceImpl(ShiftRepository shiftRepository, ChatGroupService chatGroupService,
                            WorkerRepository workerRepository, DeviceService deviceService, NotificationService notificationService, PushNotification pushNotification,
                            ShiftUpdateDtoToShift toUpdateShift, final ShiftDtoToShift toShift,
                            final AgencyService agencyService,
                            final WorkerService workerService,
                            ClientService clientService, EmailService emailService, WorkerAppliedShiftRepository repository,
                            WorkerToWorkerResultDto toWorkerResultDto, WorkerAppliedShiftRepository workerAppliedShiftRepository) {
        super();
        this.shiftRepository = shiftRepository;
        this.chatGroupService = chatGroupService;
        this.workerRepository = workerRepository;
        this.deviceService = deviceService;
        this.notificationService = notificationService;
        this.pushNotification = pushNotification;
        this.toUpdateShift = toUpdateShift;
        this.toShift = toShift;
        this.agencyService = agencyService;
        this.workerService = workerService;
        this.clientService = clientService;
        this.emailService = emailService;
        this.repository = repository;
        this.toWorkerResultDto = toWorkerResultDto;
        this.workerAppliedShiftRepository = workerAppliedShiftRepository;
    }

    @Override
    public List<BookingResultDto> save(List<ShiftCreateDto> shiftCreateDto) {

        List<Shift> shift = shiftCreateDto.stream().map(toShift::convert).collect(Collectors.toList());

        List<Shift> createdShifts = shiftRepository.saveAll(shift);


        createdShifts.forEach(createdShift->{
            if(createdShift.getPublishToAllWorkers() || createdShift.getRequireApplicationByWorkers()){
                List<WorkerShiftDTO> workerShiftDTOS = workersEligibleForShift(createdShift.getId());
                for(WorkerShiftDTO workerShiftDTO: workerShiftDTOS){
                    Worker worker = workerService.getOne(workerShiftDTO.getId());
                    log.info("Sending shift notification to : {}", worker);
                    sendEligibleShiftNotification(worker, createdShift);
                }
            }

            if (nonNull(createdShift.getWorker()) )
                sendBookedShiftNotification(createdShift.getWorker(), createdShift);

        });

        enableCarPoolingForEligibleShifts();
        return createdShifts.stream().map(toShiftResultDto::convert).collect(Collectors.toList());
    }

    @Override
    public BookingResultDto findById(Long id) {
        return toShiftResultDto.convert(shiftRepository.findById(id)
                .orElseThrow(() -> new RecordNotFoundException("Shift not found.")));
    }

    @Override
    public void authorisationReminder(Long id) {
             Shift shift = getOne(id);

            if (nonNull(shift.getLastAuthorisationReminder()) && shift.getLastAuthorisationReminder().plusDays(1).isAfter(now())) {
                throw new BusinessValidationException("Reminder already sent. Please wait another day before sending again.");
            }

            Agency agency = shift.getAgency();
            if(isNull(agency))
                throw new BusinessValidationException("Agency not set for this shift. Cannot send reminder");

            Client client = shift.getClient();
            Worker worker = shift.getWorker();


        String title = "Shift authorisation reminder";
        String body = "";

        try {
            body = "Good day. A shift authorisation request from " + worker.getFirstname() + " " + worker.getLastname() +
                    " was submitted on the MyWorklink platform. May you authorise this shift in time.\n" +
                    "Shift Details" + "\n" +
                    "Date: " + shift.getStart() + "\n" +
                    "Assignment Code: " + shift.getAssignmentCode().getCode() + "\n" +
                    "Client: " + (nonNull(shift.getClient()) ? shift.getClient().getName() : "Not set") + "\n" +
                    "Agency: " + shift.getAgency().getName() + "\n" +
                    "Location: " + (nonNull(shift.getDirectorate()) ? shift.getDirectorate().getLocation().getCity() : "") + "\n" +
                    "Directorate: " + (nonNull(shift.getDirectorate()) ? shift.getDirectorate().getName() : "") + "\n" +
                    "Start Time: " + shift.getStart().toLocalTime() + "\n" +
                    "End Time: " + shift.getEnd().toLocalTime();
        } catch (NullPointerException e) {
            body = "Shift authorisation request received. Shift ID: " + shift.getId();
        }


        String finalBody = body;
        CompletableFuture.runAsync(() -> {
                        emailService.sendSimpleMessage(agency.getEmail(), title, finalBody, agency.getId());
                    }
            );

            shift.setLastAuthorisationReminder(now());
            shiftRepository.save(shift);



    }

    @Override
    public List<Long> findByAgencyIdAndNotAdminBilled(Long agencyId) {
        return shiftRepository.findByAgencyIdAndNotAdminBilled(agencyId);
    }
    @Override
    public List<Shift> findAllByIds(List<Long> ids){
        return  shiftRepository.findAllById(ids);
    }

    @Override
    public List<BookingResultDto> findAll() {
        return shiftRepository.findAll()
                .stream()
                .map(toShiftResultDto::convert)
                .collect(Collectors.toList());
    }

    @Override
    public Page<BookingResultDto> findAllPaged(PageRequest of, LocalDate startDate, LocalDate endDate) {
        LocalDateTime startDateTime = LocalDateTime.of(startDate, LocalTime.of(0, 0));

        LocalDateTime endDateTime = LocalDateTime.of(endDate, LocalTime.of(23, 59));
        return shiftRepository.findAllShifts(startDateTime, endDateTime, of)
                .map(toShiftResultDto::convert);
    }

    @Override
    public void deleteById(Long id) {

        Shift shift = getOne(id);

        if (shift.getStatus().name().equalsIgnoreCase("NEW") || (shift.getStatus().name().equalsIgnoreCase("BOOKED"))) {
            if (shift.getStatus().name().equalsIgnoreCase("NEW")) {
                shift.setStatus(ShiftStatus.DELETED);
                shiftRepository.save(shift);
            }
            if (shift.getStatus().name().equalsIgnoreCase("BOOKED")) {
                List<Agency> agencyEmails = shift.getClient().getAgencys().stream()
                        .collect(Collectors.toList());
                Worker worker = shift.getWorker();
                shift.setStatus(ShiftStatus.CANCELLED);
                shiftRepository.save(shift);

                String message = "We regret to inform you that the shift below you had booked has been cancelled.<br><br>";
                String htmlMessage = "Shift cancelled <br><br> " +
                        "Date:" + shift.getStart() + "<br>" +
                        "Assignment Code:" + shift.getAssignmentCode().getName() + "<br>" +
                        "Client:" + shift.getClient().getName() + "<br>" +
                        "Location:" + shift.getDirectorate().getLocation().getCity() + "<br>" +
                        "Directorate:" + shift.getDirectorate().getName() + "<br>" +
                        "Start Time:" + shift.getStart().toLocalTime() + "<br>" +
                        "End Time:" + shift.getEnd().toLocalTime() + "<br>" +
                        "Please login to https://online.myworklink.uk to view full details and search for alternatives.<br>" +
                        "";//Send notification here to Agencies and Workers
                List<String> emails = new ArrayList<>();
                emails.add(worker.getEmail());


                //Push Notification
                String body = "Date:"+shift.getStart()+"\n" +
                        "Assignment Code:"+shift.getAssignmentCode().getCode()+"\n" +
                        "Client:"+shift.getClient().getName().toString()+"\n" +
                        "Location:"+shift.getDirectorate().getLocation().getCity()+"\n" +
                        "Directorate:"+shift.getDirectorate().getName()+"\n" +
                        "Start Time:"+shift.getStart().toLocalTime()+"\n" +
                        "End Time:"+shift.getEnd().toLocalTime();

                String title = "We regret to inform you that the shift below you had booked has been cancelled.\n" +
                        "\n" +
                        "Shift cancelled!";

                NotificationCreateDto notificationCreateDto = new NotificationCreateDto();

                notificationCreateDto.setTitle(title);
                notificationCreateDto.setBody(body);
                notificationCreateDto.setWorkerId(worker.getId());

                notificationService.addWorkerNotification(notificationCreateDto);
                worker.getDevices().forEach(d->{
                    CompletableFuture.runAsync(() -> {
                        try {
                            pushNotification.sendPushMessage(title, body, d.getFcmToken());
                        } catch (FirebaseMessagingException e) {
                            log.error(e.toString());
                            deviceService.delete(d);
                        }
                    });
                });
                for(Agency agency : agencyEmails)
                    emails.add(agency.getEmail());

                // Use the first agency's ID for email branding if available
                Long agencyId = agencyEmails.isEmpty() ? null : agencyEmails.iterator().next().getId();
                CompletableFuture.runAsync(() ->
                        emailService.sendMessageWithAttachment(emails, "Shift Cancellation", message.concat(htmlMessage), agencyId)
                );
            }
        } else {
            throw new BusinessValidationException("Shift cannot be deleted");
        }


    }



    @Override
    public void save(ShiftUpdateDto shiftUpdateDto) {

        Shift shift = getOne(shiftUpdateDto.getId());
        if(shift.getStatus().name().equalsIgnoreCase("IN_QUERY") || shift.getStatus().name().equalsIgnoreCase("AUTHORIZED")){
            shift.setStatus(ShiftStatus.AWAITING_AUTHORIZATION);
            shift.setReleased(true);
        }

        if(shiftUpdateDto.getNoneAttendance()){
            log.info("Setting shift status cancelled");
            shift.setStatus(ShiftStatus.CANCELLED);
        }

        shift.setQueryResponse(shiftUpdateDto.getQueryResponse());
        shift.setBreakTime(shiftUpdateDto.getBreakTime());
        shift.setStart(shiftUpdateDto.getStart());
        shift.setEnd(shiftUpdateDto.getEnd());
        shift.setShowNoteToAgency(shiftUpdateDto.getShowNoteToAgency());
        shift.setShowNoteToFw(shiftUpdateDto.getShowNoteToFw());
        enableCarPoolingForEligibleShifts();

        toShiftResultDto.convert(shiftRepository.saveAndFlush(
                shift));
    }

    @Override
    public void book(Long shiftId, Long agencyId, Long workerId) throws BusinessValidationException, JsonProcessingException {
        Worker worker = workerService.getOne(workerId);
        Agency agency = agencyService.getOne(agencyId);
        Shift shift = getOne(shiftId);
        boolean isAvailable = workerService.getWorkerConflicts(workerId, shift.getStart(), shift.getEnd());

        if (shift.getRequireApplicationByWorkers())
            throw new BusinessValidationException("Shift requires that workers apply");
        if (!isAvailable)
            throw new BusinessValidationException("Worker cannot book this shift because of conflicting shifts");
        if (shift.getStatus() != ShiftStatus.NEW)
            throw new BusinessValidationException("Shift no longer available for booking");


        shift.setAgency(agency);
        shift.setWorker(worker);
        shift.setBookedDate(now());
        shift.setStatus(ShiftStatus.BOOKED);
        log.info("Response book shift: {}", shift);
        shiftRepository.save(shift);
        checkWorkerCompliance(shift.getAgency(), worker, shift);
        sendBookedShiftNotification(worker, shift);
    }

    @Override
    public void apply(Long shiftId, Long agencyId, Long workerId) {
        Shift shift = getOne(shiftId);
        Agency agency = agencyService.getOne(agencyId);
        Worker worker = workerService.getOne(workerId);
        boolean isAvailable = workerService.getWorkerConflicts(workerId, shift.getStart(), shift.getEnd());

        if (!isAvailable)
            throw new BusinessValidationException("Worker cannot book this shift because of conflicting shifts");
        if (!shift.getRequireApplicationByWorkers())
            throw new BusinessValidationException("Worker cannot apply for this shift");
        if (LocalDateTime.now().isAfter(shift.getStart())) {
                log.info("Shift {} has expired",shiftId);
                shift.setStatus(ShiftStatus.EXPIRED);
                shiftRepository.save(shift);
                throw new BusinessValidationException("Shift time already passed.");
        }
        if (shift.getStatus() != ShiftStatus.NEW) {
                log.info("Shift {} already booked", shiftId);
                throw new BusinessValidationException("Shift already booked.");
        }

        shift.setAgency(agency);
        shift.setAppliedStatus(ShiftStatus.APPLIED);
        shiftRepository.save(shift);
        WorkerAppliedShift workerAppliedShift = new WorkerAppliedShift();
        workerAppliedShift.setShift(shift);
        workerAppliedShift.setAgency(agency);
        workerAppliedShift.setWorker(worker);
        workerAppliedShift.setAppliedDate(LocalDate.now());
        workerAppliedShift.setShiftStatus(ShiftStatus.APPLIED);
        try{
            repository.save(workerAppliedShift);
        }catch (DataIntegrityViolationException dataIntegrityViolationException){
            throw new BusinessValidationException("You already applied for this shift");
        }
    }




    @Override
    public void approve(Long shiftId, Long agencyId, Long workerId) {
        Worker worker = workerService.getOne(workerId);
        Shift shift = getOne(shiftId);

        boolean isAvailable = workerService.getWorkerConflicts(workerId, shift.getStart(), shift.getEnd());
        if(!isAvailable)
            throw new BusinessValidationException("Worker cannot book this shift because of conflicting shifts");
        if(LocalDateTime.now().isAfter(shift.getStart())){
            shift.setStatus(ShiftStatus.EXPIRED);
            shiftRepository.save(shift);
            throw new BusinessValidationException("Shift date already passed.");
        }
        if (shift.getStatus() != ShiftStatus.NEW)
            throw new BusinessValidationException("Shift already booked.");


        shift.setWorker(worker);
        shift.setBookedDate(now());
        shift.setStatus(ShiftStatus.BOOKED);
        log.info("Response book shift: {}", shift.toString());
        shiftRepository.save(shift);
        sendBookedShiftNotification(worker, shift);
    }

    @Override
    public void bookWorkerDirectly(Long shiftId, Long workerId) {
        Shift shift = getOne(shiftId);
        Worker worker = workerService.getOne(workerId);

        if (shift.getRequireApplicationByWorkers())
            throw new BusinessValidationException("Shift requires that workers apply");
        if (shift.getStatus() != ShiftStatus.NEW)
            throw new BusinessValidationException("Shift already booked.");
        boolean isAvailable = workerService.getWorkerConflicts(workerId, shift.getStart(), shift.getEnd());
        if(!isAvailable)
            throw new BusinessValidationException("Worker cannot book this shift because of conflicting shifts");

        shift.setWorker(worker);
        shift.setBookedDate(now());
        shift.setStatus(ShiftStatus.BOOKED);
        log.info("Response book shift: {}", shift.toString());
        shiftRepository.save(shift);
        WorkerAppliedShift workerAppliedShift = new WorkerAppliedShift();
        workerAppliedShift.setShift(shift);
        workerAppliedShift.setAgency(shift.getAgency());
        workerAppliedShift.setWorker(worker);
        workerAppliedShift.setAppliedDate(LocalDate.now());
        workerAppliedShift.setShiftStatus(ShiftStatus.BOOKED);

        try {
            repository.save(workerAppliedShift);
        }catch (DataIntegrityViolationException dataIntegrityViolationException){
            throw new BusinessValidationException("You already applied for this shift");

        }
        checkWorkerCompliance(shift.getAgency(), worker, shift);
        sendBookedShiftNotification(worker, shift);
    }

    private void checkWorkerCompliance(Agency agency, Worker worker, Shift shift) {
        Boolean compliant = worker.checkCompliance();
        if(!compliant){
            String titleA = "Compliance issues on booked shift!";
            String bodyA = "Good day\n Please check detected compliance issues for a recently booked worker " +
                    worker.getFirstname()+" "+ worker.getLastname()+"on shift "+shift.getId().toString()+".\n" +
                    "Check the shift details below.\n" +
                    "Shift booked\n"+
                    "Date:"+shift.getStart()+"\n" +
                    "Assignment Code:"+shift.getAssignmentCode().getCode()+"\n" +
                    "Client:"+shift.getClient().getName().toString()+"\n" +
                    "Location:"+shift.getDirectorate().getLocation().getCity()+"\n" +
                    "Directorate:"+shift.getDirectorate().getName()+"\n" +
                    "Start Time:"+shift.getStart().toLocalTime()+"\n" +
                    "End Time:"+shift.getEnd().toLocalTime();

            String title = "Compliance issues detected";
            String body = "Good day "+worker.getFirstname()+" "+ worker.getLastname()+".\n" +
                    "Please check detected compliance issues for a recently booked shift: "+shift.getId().toString()+".\n" +
                    "Check the shift details below.\n" +
                    "Shift booked\n"+
                    "Date:"+shift.getStart()+"\n" +
                    "Assignment Code:"+shift.getAssignmentCode().getCode()+"\n" +
                    "Client:"+shift.getClient().getName().toString()+"\n" +
                    "Location:"+shift.getDirectorate().getLocation().getCity()+"\n" +
                    "Directorate:"+shift.getDirectorate().getName()+"\n" +
                    "Start Time:"+shift.getStart().toLocalTime()+"\n" +
                    "End Time:"+shift.getEnd().toLocalTime();

            CompletableFuture.runAsync(()-> emailService.sendSimpleMessage(worker.getEmail(), title, body));
            CompletableFuture.runAsync(()-> emailService.sendSimpleMessage(agency.getEmail(), title, body, agency.getId()));
        }
    }

    @Override
    public void cancelWorkerShift(Long shiftId, Long workerId, String reason) {

            Shift shift = getOne(shiftId);
            log.info("Inside cancel shift by worker");
            if (shift.getWorker().getId() == workerId) {
                log.info("Worker is same on shift");
                if (shift.getStatus() == ShiftStatus.BOOKED ) {
                    log.info("Attempt to cancel booked shift");
                    shift.setStatus(ShiftStatus.NEW);
                    shift.setCancelReason(reason);
                    shift.setCancelledDate(now());
                    log.info("Response cancel shift: {}", shift.toString());
                    shiftRepository.save(shift);
                }
                else if (shift.getAppliedStatus() == ShiftStatus.APPLIED) {

                    List<WorkerAppliedShift> workerAppliedShift = workerAppliedShiftRepository.findWorkerAppliedShift(shiftId, workerId);
                    for (int i = 0; i < workerAppliedShift.size(); i++) {
                        WorkerAppliedShift appliedShift = workerAppliedShift.get(i);
                        workerAppliedShiftRepository.delete(appliedShift);
                    }

                    log.info("Response cancel APPLIED shiftId, workerid: {}{}", shiftId, workerId);
                    shiftRepository.save(shift);
                } else {
                    throw new BusinessValidationException("Shift cannot be cancelled because its now in : " + shift.getStatus() + " status.");
                }
            }
            else if (shift.getStatus() == ShiftStatus.NEW ) {
                shift.setCancelledDate(now());
                log.info("Response cancel shift: {}", shift.toString());
                shiftRepository.save(shift);
                if(shift.getAppliedStatus().toString().equalsIgnoreCase(ShiftStatus.APPLIED.toString())){
                    List<WorkerAppliedShift> workerAppliedShifts = workerAppliedShiftRepository.findWorkerAppliedShift(shiftId, workerId);

                    try {
                        for (int i = 0; i < workerAppliedShifts.size(); i++) {
                            WorkerAppliedShift appliedShift = workerAppliedShifts.get(i);
                            workerAppliedShiftRepository.delete(appliedShift);
                            workerAppliedShiftRepository.flush();
                        }

                    } catch (Exception ex) {
                        throw new BusinessValidationException("This applied shift cannot be deleted.");
                    }
                }
            }
            else {
                throw new BusinessValidationException("Worker cannot cancel this shift as it has been booked by another worker");
            }

    }

    @Override
    public List<BookingResultDto> filter(Long workerId, LocalDate startDate, LocalDate endDate, String status, PageRequest of) {
        Set<BookingResultDto> bookingResultDtoList = getShifts(workerId, status);
        return bookingResultDtoList.stream()
                .filter(shiftResultDto -> checkAfter(shiftResultDto.getStart(), startDate))
                .filter(shiftResultDto -> checkBefore(shiftResultDto.getStart(), endDate))
                .collect(Collectors.toList());
    }

    private boolean dateBetween(String shiftDate, LocalDate startDate, LocalDate endDate) {

        return true;
    }

    @Override
    public List<BookingResultDto> filterByAgent(Long workerId, Long agentId, LocalDate startDate, LocalDate endDate, String status, PageRequest of) {
        Set<BookingResultDto> bookingResultDtoList = getShifts(workerId, status);
        return bookingResultDtoList.stream()
                .filter(shiftResultDto -> checkAfter(shiftResultDto.getStart(), startDate))
                .filter(shiftResultDto -> checkBefore(shiftResultDto.getStart(), endDate))
                .filter(shiftResultDto -> shiftResultDto.getAgency().equalsIgnoreCase(agencyService.getOne(agentId).getName()))
                .collect(Collectors.toList());
    }


    @Override
    public List<BookingResultDto> filterByClient(Long workerId, Long clientId, LocalDate startDate, LocalDate endDate, String status, PageRequest of) {

        Set<BookingResultDto> bookingResultDtoList = getShifts(workerId, status);
        return bookingResultDtoList.stream()
                .filter(shiftResultDto -> checkAfter(shiftResultDto.getStart(), startDate))
                .filter(shiftResultDto -> checkBefore(shiftResultDto.getStart(), endDate))
                //.filter(shiftResultDto -> shiftResultDto.getAgency().equalsIgnoreCase(agencyService.getOne(payeeId).getCity()))
                .collect(Collectors.toList());
    }

    @Override
    public List<BookingResultDto> filterByClientAndAgent(Long workerId, Long agentId, Long clientId, String location, LocalDate startDate, LocalDate endDate, String status, PageRequest of) {

        Set<BookingResultDto> bookingResultDtoList = getShifts(workerId, status);
        return bookingResultDtoList.stream()
                .filter(shiftResultDto -> checkAfter(shiftResultDto.getStart(), startDate))
                .filter(shiftResultDto -> checkBefore(shiftResultDto.getStart(), endDate))
                .filter(shiftResultDto -> shiftResultDto.getAgency() != null && shiftResultDto.getAgency().equalsIgnoreCase(agencyService.getOne(agentId).getName()))
                .filter(shiftResultDto -> shiftResultDto.getClient() != null && shiftResultDto.getClient().equalsIgnoreCase(clientService.getOne(clientId).getName()))
                .filter(shiftResultDto -> shiftResultDto.getShiftLocation().equalsIgnoreCase(location))
                .collect(Collectors.toList());
    }

    @Override
    public List<BookingResultDto> filterByClientAndAgent(Long workerId, Long agentId, Long clientId, LocalDate startDate, LocalDate endDate, String status, PageRequest of) {
        Set<BookingResultDto> bookingResultDtoList = getShifts(workerId, status);
        return bookingResultDtoList.stream()
                .filter(shiftResultDto -> checkAfter(shiftResultDto.getStart(), startDate))
                .filter(shiftResultDto -> checkBefore(shiftResultDto.getStart(), endDate))
                .filter(shiftResultDto -> shiftResultDto.getAgency() != null && shiftResultDto.getAgency().equalsIgnoreCase(agencyService.getOne(agentId).getName()))
                .filter(shiftResultDto -> shiftResultDto.getClient() != null && shiftResultDto.getClient().equalsIgnoreCase(clientService.getOne(clientId).getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<BookingResultDto> filterByClient(Long workerId, Long clientId, String location, LocalDate startDate, LocalDate endDate, String status, PageRequest of) {
        Set<BookingResultDto> bookingResultDtoList = getShifts(workerId, status);
        return bookingResultDtoList.stream()
                .filter(shiftResultDto -> checkAfter(shiftResultDto.getStart(), startDate))
                .filter(shiftResultDto -> checkBefore(shiftResultDto.getStart(), endDate))
                .filter(shiftResultDto -> shiftResultDto.getClient() != null && shiftResultDto.getClient().equalsIgnoreCase(clientService.getOne(clientId).getName()))
                .filter(shiftResultDto -> shiftResultDto.getShiftLocation().equalsIgnoreCase(location))
                .collect(Collectors.toList());
    }

    @Override
    public List<BookingResultDto> filterByAgent(Long workerId, Long agentId, String location, LocalDate startDate, LocalDate endDate, String status, PageRequest of) {
        Set<BookingResultDto> bookingResultDtoList = getShifts(workerId, status);
        return bookingResultDtoList.stream()
                .filter(shiftResultDto -> checkAfter(shiftResultDto.getStart(), startDate))
                .filter(shiftResultDto -> checkBefore(shiftResultDto.getStart(), endDate))
                .filter(shiftResultDto -> shiftResultDto.getAgency() != null && shiftResultDto.getAgency().equalsIgnoreCase(agencyService.getOne(agentId).getName()))
                .filter(shiftResultDto -> shiftResultDto.getShiftLocation().equalsIgnoreCase(agencyService.getOne(agentId).getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<BookingResultDto> filter(Long workerId, String location, LocalDate startDate, LocalDate endDate, String status, PageRequest of) {
        Set<BookingResultDto> bookingResultDtoList = getShifts(workerId, status);
        return bookingResultDtoList.stream()
                .filter(shiftResultDto -> checkAfter(shiftResultDto.getStart(), startDate))
                .filter(shiftResultDto -> checkBefore(shiftResultDto.getStart(), endDate))
                .filter(shiftResultDto -> shiftResultDto.getShiftLocation().equalsIgnoreCase(location))
                .collect(Collectors.toList());
    }

    @Override
    public void save(ShiftUpdateDto shiftUpdateDto, String reason) {
        try {
            Shift shift = getOne(shiftUpdateDto.getId());

            if (shift.getStatus().name().equalsIgnoreCase("WAITING")
                    || shift.getStatus().name().equalsIgnoreCase("AUTHORIZED")
                    || shift.getStatus().name().equalsIgnoreCase("EXPIRED")
                    || shift.getStatus().name().equalsIgnoreCase("DELETED")) {
                throw new BusinessValidationException("Shift cannot be edited");
            }
            else {
                if (!shift.getStatus().name().equalsIgnoreCase("IN_QUERY")) {
                    throw new BusinessValidationException("Use this function to amend shift in query");
                }
                if(shiftUpdateDto.getNoneAttendance()){
                    log.info("Setting shift status cancelled");
                    shiftUpdateDto.setShiftStatus(ShiftStatus.CANCELLED);

                }else{
                    log.info("Setting shift status {}", shiftUpdateDto.getShiftStatus());
                    shiftUpdateDto.setShiftStatus(ShiftStatus.AUTHORIZED);
                }
                shiftUpdateDto.setShiftStatus(ShiftStatus.AUTHORIZED);
                Shift newshift = toUpdateShift.convert(shiftUpdateDto);
                newshift.setId(shift.getId());

                if (shift.getAgency() != null) {
                    newshift.setAgency(shift.getAgency());
                }
                if (shift.getClient() != null) {
                    newshift.setClient(shift.getClient());
                }
                if (shift.getAgencies() != null) {
                    newshift.setAgencies(shift.getAgencies());
                }

                toShiftResultDto.convert(shiftRepository.save(
                        newshift));
                Worker worker = shift.getWorker();
                List<String> emails = new ArrayList<>();
                emails.add(worker.getEmail());

                CompletableFuture.runAsync(() ->
                        emailService.sendMessageWithAttachment(emails, "Shift Response", reason)
                );
            }
        } catch (Exception exception) {
            throw new BusinessValidationException(exception.getMessage());
        }
    }


    @Override
    public Page<BookingResultDto> findAllClientPaged(Long clientId, PageRequest of) {
        log.info("I am here now and I dont understand");
        return shiftRepository.findAllPagedClients(clientId, of)

                .map(toShiftResultDto::convert);
    }

    @Override
    public void authorize(Long shiftId) throws BusinessValidationException {
        try {
            Shift shift = getOne(shiftId);
//            Client client = clientService.getOne(clientId);
//            if (Objects.equals(shift.getClient().getId(), client.getId())) {
                if (shift.getStatus() == ShiftStatus.AWAITING_AUTHORIZATION) {
                    shift.setStatus(ShiftStatus.AUTHORIZED);
                    shift.setAuthorizedDate(now());
                    log.info("Response authorize shift: {}", shift.toString());
                    shiftRepository.save(shift);
                    //rabbitService.publish("x.shift", "authorize", shift);
                } else {
                    throw new BusinessValidationException("Shift not yet booked.");
                }
//            } else {
//                throw new BusinessValidationException("Client cannot authorize this shift");
//            }
        } catch (Exception exception) {
            throw new BusinessValidationException(exception.getMessage());
        }
    }

    @Override
    public void release(Long shiftId, Long workerId) throws BusinessValidationException {
        try {
            Shift shift = getOne(shiftId);
            Worker worker = workerService.getOne(workerId);
            if (shift.getWorker().getId() == worker.getId()) {
//                if (shift.getStatus() == ShiftStatus.AUTHORIZED) {
                shift.setReleased(true);
                shift.setReleaseDate(now());
                log.info("Response release shift: {}", shift.toString());
                shiftRepository.save(shift);
                //rabbitService.publish("x.shift", "authorize", shift);
//                } else {
//                    throw new BusinessValidationException("Shift not yet booked.");
//                }
            } else {
                throw new BusinessValidationException("You are not authorized to release this shift");
            }
        } catch (Exception exception) {
            throw new BusinessValidationException(exception.getMessage());
        }
    }

    @Override
    public void authorizeQueried(Long shiftId, Long clientId, LocalTime endTime) throws BusinessValidationException {
        try {
            Shift shift = getOne(shiftId);
            Client client = clientService.getOne(clientId);
            if (shift.getClient().getId() == client.getId()) {
                if (shift.getStatus() == ShiftStatus.IN_QUERY) {
                    shift.setStatus(ShiftStatus.AUTHORIZED);
                    shift.setEnd(shift.getEnd().toLocalDate().atTime(endTime));
                    shift.setAuthorizedDate(now());
                    log.info("Response authorize shift: {}", shift.toString());
                    shiftRepository.save(shift);
                    //rabbitService.publish("x.shift", "queried", shift);
                } else {
                    throw new BusinessValidationException("Shift cannot be authorized.");
                }
            } else {
                throw new BusinessValidationException("Client cannot authorize this shift");
            }
        } catch (Exception exception) {
            throw new BusinessValidationException(exception.getMessage());
        }
    }

    @Override
    public ShiftReportStatus findAllShiftsByStatus() {


        Page<BookingResultDto> authShifts = findAllPagedByStatus( PageRequest.of(0, 10), ShiftStatus.AUTHORIZED.name());
        Page<BookingResultDto> appliedShifts = findAllPagedByStatus( PageRequest.of(0, 10), ShiftStatus.APPLIED.name());
        Page<BookingResultDto> awaitingShifts = findAllPagedByStatus( PageRequest.of(0, 10), ShiftStatus.AWAITING_AUTHORIZATION.name());
        Page<BookingResultDto> bookedShifts = findAllPagedByStatus( PageRequest.of(0, 10), ShiftStatus.BOOKED.name());
        Page<BookingResultDto> billedShifts = findAllPagedByStatus( PageRequest.of(0, 10), ShiftStatus.BILLED.name());
        Page<BookingResultDto> cancelledShifts = findAllPagedByStatus( PageRequest.of(0, 10), ShiftStatus.CANCELLED.name());
        Page<BookingResultDto> newShifts = findAllPagedByStatus( PageRequest.of(0, 10), ShiftStatus.NEW.name());
        Page<BookingResultDto> queryShifts = findAllPagedByStatus( PageRequest.of(0, 10), ShiftStatus.IN_QUERY.name());



        ShiftReportStatus shiftReportStatus = new ShiftReportStatus();

        shiftReportStatus.setNewShift(newShifts.getTotalElements());
        shiftReportStatus.setAuthorized(authShifts.getTotalElements());
        shiftReportStatus.setQueried(queryShifts.getTotalElements());
        shiftReportStatus.setBooked(bookedShifts.getTotalElements());
        shiftReportStatus.setBilled(billedShifts.getTotalElements());
        shiftReportStatus.setCancelled(cancelledShifts.getTotalElements());
        shiftReportStatus.setApplied(appliedShifts.getTotalElements());
        shiftReportStatus.setAwaiting(awaitingShifts.getTotalElements());



        return shiftReportStatus;


    }

    @Override
    public Page<BookingResultDto> findAllStatusPaged(String status, PageRequest of, LocalDate startDate, LocalDate endDate) {
        LocalDateTime startDateTime = LocalDateTime.of(startDate, LocalTime.of(0, 0));

        LocalDateTime endDateTime = LocalDateTime.of(endDate, LocalTime.of(23, 59));

        return shiftRepository.findAllPaged(status, of, startDateTime, endDateTime)
                .map(toShiftResultDto::convert);
    }

    @Override
    public Page<BookingResultDto> findAllStatusByClientPaged(Long clientId, String status, PageRequest of, LocalDate startDate, LocalDate endDate) {

        LocalDateTime startDateTime = LocalDateTime.of(startDate, LocalTime.of(0, 0));
        LocalDateTime endDateTime = LocalDateTime.of(endDate, LocalTime.of(23, 59));
        return shiftRepository.findAllPagedClient(status, clientId, of, startDateTime, endDateTime)

                .map(toShiftResultDto::convert);
    }

    @Override
    public void query(Long shiftId, Long workerId, String reason) {
        try {
            Shift shift = getOne(shiftId);
//            Worker worker = workerService.getOne(workerId);
//            if (shift.getWorker().getId() == workerId) {
            if (shift.getStatus() == ShiftStatus.AUTHORIZED) {
                shift.setStatus(ShiftStatus.IN_QUERY);
                shift.setQueryReason(reason);
                shift.setQueriedDate(now());
                log.info("Response authorize shift: {}", shift.toString());
                shiftRepository.save(shift);
                //rabbitService.publish("x.shift", "query", shift);
            } else {
                throw new BusinessValidationException("Shift not yet authorized.");
            }
//            } else {
//                throw new BusinessValidationException("Worker cannot get this shift into query status as it has been booked by another worker");
//            }
        } catch (Exception exception) {
            throw new BusinessValidationException(exception.getMessage());
        }

    }

    @Override
    public void cancel(Long shiftId, Long clientId, String reason) {
        try {
            Shift shift = getOne(shiftId);
            Client client = clientService.getOne(clientId);
            if (shift.getClient().getId() == clientId) {
                if ((shift.getStatus() == ShiftStatus.NEW) || (shift.getStatus() == ShiftStatus.BOOKED)) {
                    shift.setStatus(ShiftStatus.CANCELLED);
                    shift.setCancelReason(reason);
                    shift.setCancelledDate(now());
                    log.info("Response authorize shift: {}", shift.toString());
                    shiftRepository.save(shift);
                    //rabbitService.publish("x.shift", "cancel", shift);

                    List<Agency> agencyEmails = shift.getClient().getAgencys().stream()
                            .collect(Collectors.toList());
                    Worker worker = shift.getWorker();
                    shift.setStatus(ShiftStatus.CANCELLED);
                    shiftRepository.save(shift);
//                    sendEmail(shift.getClient().getId());




                    String message = "Shift Cancelled";
                    String htmlMessage = "We regret to inform you that the shift below you had booked has been cancelled.\nShift cancelled <br>" +
                            "Date:" + shift.getStart() + "<br>" +
                            "Assignment Code:" + shift.getAssignmentCode().getName() + "<br>" +
                            "Client:" + shift.getClient().getName() + "<br>" +
                            "Location:" + shift.getDirectorate().getLocation().getCity() + "<br>" +
                            "Directorate:" + shift.getDirectorate().getName() + "<br>" +
                            "Start Time:" + shift.getStart().toLocalTime() + "<br>" +
                            "End Time:" + shift.getEnd().toLocalTime() + "<br>" +
                            "Please login to "+ iosAppUrl + " to view full details and search for alternatives.<br>";//Send notification here to Agencies and Workers
                    List<String> emails = new ArrayList<>();

                    worker.getDevices().forEach(d->{
                        CompletableFuture.runAsync(() -> {
                            try {
                                pushNotification.sendPushMessage(message,htmlMessage, d.getFcmToken());
                            } catch (FirebaseMessagingException e) {
                                log.error(e.toString());
                                deviceService.delete(d);
                            }
                        });
                    });
                    emails.add(worker.getEmail());
                    for (Agency agency : agencyEmails
                    ) {
                        emails.add(agency.getEmail());
                    }

                    // Use the first agency's ID for email branding if available
                    Long agencyId = agencyEmails.isEmpty() ? null : agencyEmails.iterator().next().getId();
                    CompletableFuture.runAsync(() ->
                            emailService.sendMessageWithAttachment(emails, "Shift Cancellation", message.concat(htmlMessage), agencyId)
                    );
                } else {
                    throw new BusinessValidationException("Shift cannot be cancelled.");
                }
            } else {
                throw new BusinessValidationException("Client cannot cancel this shift as it does not belong to this client.");
            }
        } catch (Exception ex) {
            throw new BusinessValidationException(ex.getMessage());
        }
    }

    @Override
    public Page<BookingResultDto> findAllPagedByStatus(PageRequest of, String status) {
        if(ShiftStatus.CANCELLED.toString().equalsIgnoreCase(status)||ShiftStatus.BILLED.toString().equalsIgnoreCase(status)){
            log.info("Shift  status cancelled for admin not less that 30 days ago: {}", status);
            LocalDate date;
            date =  LocalDate.now().minusDays(30);

            return shiftRepository.findByStatusAndDate( status, date, of).map(toShiftResultDto::convert);
        }
        return shiftRepository.findAllByStatus(status, of)
                .map(toShiftResultDto::convert);
    }

    @Override
    public Page<BookingResultDto> findAllStatusByClientIdPaged(Long clientId, String status, PageRequest of) {
        Page<Shift> shiftList;
        if(ShiftStatus.CANCELLED.toString().equalsIgnoreCase(status) || ShiftStatus.BILLED.toString().equalsIgnoreCase(status)){
            log.info("Shift  status cancelled for admin not less that 30 days ago: {}", status);
            LocalDate date;
            date =  LocalDate.now().minusDays(30);

            shiftList = shiftRepository.findByStatusAndDateAndClientId( clientId, status, date, of);
        }else if(ShiftStatus.AUTHORIZED.toString().equalsIgnoreCase(status)){
            log.info("Shift  status cancelled for admin not less that 30 days ago: {}", status);
            LocalDate date;
            date =  LocalDate.now().minusDays(30);

            shiftList = shiftRepository.findClientAuthorized( clientId, status, date, of);
        }else{
            shiftList= shiftRepository.findAllPagedClientIdStatus(clientId, status, of);
        }

//        for(Shift s: shiftList){
//            List<Worker> applicants = workerRepository.findWorkersOnShift(s.getId());
//            log.info("Shift applicants for applied page in agency: {}", applicants);
//            s.setApplicantCount((long) applicants.size());
//        }

        Page<Shift> page = shiftList;
        return page.map(toShiftResultDto::convert);
    }


    @Override
    public Page<BookingResultDto> findJobCount(Long workerId) {
        List<IShiftReportStatus> shiftReport = shiftRepository.countWorkerShiftsByStatus(workerId);
        int hrs = shiftRepository.findJobHrs(workerId);

        JobCountResult shiftCount =  new JobCountResult();
        shiftCount.setJobs(shiftReport.get(0).getBilled());
        shiftCount.setHrs(shiftReport.get(0).getBilled());


        return null;
    }

    @Override
    public ShiftReportStatus findAllClientShiftsByStatus(Long clientId) {

        Page<BookingResultDto> authShifts = findAllStatusByClientIdPaged(clientId, ShiftStatus.AUTHORIZED.name(), PageRequest.of(0, 10));
        Page<BookingResultDto> appliedShifts = findAllStatusByClientIdPaged(clientId, ShiftStatus.APPLIED.name(), PageRequest.of(0, 10));
        Page<BookingResultDto> awaitingShifts = findAllStatusByClientIdPaged(clientId, ShiftStatus.AWAITING_AUTHORIZATION.name(), PageRequest.of(0, 10));
        Page<BookingResultDto> bookedShifts = findAllStatusByClientIdPaged(clientId, ShiftStatus.BOOKED.name(), PageRequest.of(0, 10));
        Page<BookingResultDto> billedShifts = findAllStatusByClientIdPaged(clientId, ShiftStatus.BILLED.name(), PageRequest.of(0, 10));
        Page<BookingResultDto> cancelledShifts = findAllStatusByClientIdPaged(clientId, ShiftStatus.CANCELLED.name(), PageRequest.of(0, 10));
        Page<BookingResultDto> newShifts = findAllStatusByClientIdPaged(clientId, ShiftStatus.NEW.name(), PageRequest.of(0, 10));
        Page<BookingResultDto> queryShifts = findAllStatusByClientIdPaged(clientId, ShiftStatus.IN_QUERY.name(), PageRequest.of(0, 10));



        ShiftReportStatus shiftReportStatus = new ShiftReportStatus();

        shiftReportStatus.setNewShift(newShifts.getTotalElements());
        shiftReportStatus.setAuthorized(authShifts.getTotalElements());
        shiftReportStatus.setQueried(queryShifts.getTotalElements());
        shiftReportStatus.setBooked(bookedShifts.getTotalElements());
        shiftReportStatus.setBilled(billedShifts.getTotalElements());
        shiftReportStatus.setCancelled(cancelledShifts.getTotalElements());
        shiftReportStatus.setApplied(appliedShifts.getTotalElements());
        shiftReportStatus.setAwaiting(awaitingShifts.getTotalElements());



        return shiftReportStatus;
    }

    @Override
    public ShiftReportStatus findAllAgencyShiftsByStatus(Long agencyId) {
        Page<BookingResultDto> authShifts = agencyService.findAllShiftsAgencyPagedByStatus(agencyId, PageRequest.of(0, 10), ShiftStatus.AUTHORIZED.name(), null, null, null);
        Page<BookingResultDto> awaitingShifts = agencyService.findAllShiftsAgencyPagedByStatus(agencyId,  PageRequest.of(0, 10), ShiftStatus.AWAITING_AUTHORIZATION.name(), null, null, null);
        Page<BookingResultDto> bookedShifts = agencyService.findAllShiftsAgencyPagedByStatus(agencyId,  PageRequest.of(0, 10), ShiftStatus.BOOKED.name(), null, null, null);
        Page<BookingResultDto> billedShifts = agencyService.findAllShiftsAgencyPagedByStatus(agencyId,  PageRequest.of(0, 10), ShiftStatus.BILLED.name(), null, null, null);
        Page<BookingResultDto> cancelledShifts = agencyService.findAllShiftsAgencyPagedByStatus(agencyId,  PageRequest.of(0, 10), ShiftStatus.CANCELLED.name(), null, null, null);
        Page<BookingResultDto> newShifts = agencyService.findAllShiftsAgencyPagedByStatus(agencyId,  PageRequest.of(0, 10), ShiftStatus.NEW.name(), null, null, null);
        Page<BookingResultDto> queryShifts = agencyService.findAllShiftsAgencyPagedByStatus(agencyId,  PageRequest.of(0, 10), ShiftStatus.IN_QUERY.name(), null, null, null);

        ShiftReportStatus shiftReportStatus = new ShiftReportStatus();

        shiftReportStatus.setNewShift(newShifts.getTotalElements());
        shiftReportStatus.setAuthorized(authShifts.getTotalElements());
        shiftReportStatus.setQueried(queryShifts.getTotalElements());
        shiftReportStatus.setBooked(bookedShifts.getTotalElements());
        shiftReportStatus.setBilled(billedShifts.getTotalElements());
        shiftReportStatus.setCancelled(cancelledShifts.getTotalElements());
        shiftReportStatus.setAwaiting(awaitingShifts.getTotalElements());
        shiftReportStatus.setApplied(newShifts.stream().filter(e->e.getAppliedStatus()==ShiftStatus.APPLIED).count());


        return shiftReportStatus;
    }

    @Override
    public List<IShiftReportStatus> findAllWorkerShiftsByStatus(Long workerId) {
        return shiftRepository.countWorkerShiftsByStatus(workerId);
    }


    @Override
    public Shift getOne(Long id) {
        return shiftRepository.findById(id)
                .orElseThrow(() -> new BusinessValidationException("Shift not found"));
    }

    @Override
    public void save(Shift shift) {
        shiftRepository.save(shift);
    }


    @Override
    public List<BookingResultDto> findAppliedShiftsForWorkerId(Long workerId, PageRequest of) {
        Worker worker = workerService.getOne(workerId);


        List<BookingResultDto> shifts = new ArrayList<>();
        List<WorkerAppliedShift>
                appliedShifts =
                workerAppliedShiftRepository
                        .findWorkerAppliedShifts(
                                workerId
                        );

        appliedShifts.stream().forEach((c) ->
                shifts.add(toShiftResultDto.convert(c.getShift()))
        );

        shifts.removeIf(s -> s.getShiftStatus().equals(ShiftStatus.NEW));


        shifts.addAll(transportBookingService.findWokerBookingByStatus(ShiftStatus.APPLIED, workerId));



        return shifts;
    }


    private boolean dateDiff(LocalDate now, LocalDate shiftDate) {
        return shiftDate.compareTo(now) >= 0;
    }

    private boolean dateEqual(LocalDate now, LocalDate shiftDate) {
        return shiftDate.compareTo(now) == 0;
    }

    private boolean timeDiff(LocalTime now, String shiftTime) {
        return LocalTime.parse(shiftTime).compareTo(now) >= 0;
    }

    private boolean timeDiff(String endTime, String shiftTime) {
        return LocalTime.parse(shiftTime).compareTo(LocalTime.parse(endTime)) >= 0;
    }


    Set<BookingResultDto> getShifts(Long workerId, String status) {
        if (status.equalsIgnoreCase("NEW")) {
            return findAllShiftsForWorkerIdAndStatus(workerId, PageRequest.of(0, 100), status);
        } else {
            return findAllShiftsForWorkerIdAndStatus(workerId, PageRequest.of(0, 100), status);
        }
    }

    private boolean checkAfter(LocalDateTime leftDate, LocalDate rightDate) {
        return !leftDate.toLocalDate().isBefore(rightDate);
    }

    private boolean checkBefore(LocalDateTime leftDate, LocalDate rightDate) {
        return !leftDate.toLocalDate().isAfter(rightDate);
    }

    private LocalDate convertFromString(String aDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return LocalDate.parse(aDate, formatter);
        } catch (DateTimeParseException e) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            return LocalDate.parse(aDate, formatter);
        }
    }





    @Override
    public Set<BookingResultDto> findAllShiftsForWorkerIdAndStatus(Long workerId, PageRequest of, String status) {
        Worker worker = workerService.getOne(workerId);
        Set<BookingResultDto> shiftres = new HashSet<>();
        Set<BookingResultDto> eligibleBookings  = new HashSet<>();
        log.info("I am here with this loads of shift");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");


        if (status.equalsIgnoreCase(ShiftStatus.NEW.toString())) {
            Set<BookingResultDto> bookingResults = transportBookingService.findNewBookingsForWorker(workerId);
            log.info("################ Status {}, {}", status, ShiftStatus.NEW.toString());
            List<BookingResultDto> bookingResultDtos = shiftRepository.findShiftsPagedStatus(workerId, status, worker.getGender().toString(), Integer.valueOf(Math.toIntExact(worker.getAssignmentCode().getId())), of)
                    .stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList());
            bookingResults.addAll(bookingResultDtos);
            bookingResults.addAll(eligibleBookings);
            return bookingResults;
        } else if(status.equalsIgnoreCase(ShiftStatus.AUTHORIZED.toString())){
            log.info("################ Status {}", status);
            Set<BookingResultDto> bookingsResults = transportBookingService.findWokerBookingByStatus(ShiftStatus.AUTHORIZED, workerId);
            shiftres.addAll(workerTrainingSessionService.findWorkerBookings(workerId, WorkerTrainingSessionStatus.AUTHORIZED));
            shiftres.addAll(shiftRepository.findShiftsAuthosizedAndUnpaid(workerId, status, of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList()));
            shiftres.addAll(shiftRepository.findShiftsAuthosizedAndUnpaid(workerId,ShiftStatus.BILLED.toString(), of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList()));
            shiftres.addAll(bookingsResults);
            return shiftres;
        } else if(status.equalsIgnoreCase(ShiftStatus.AWAITING_AUTHORIZATION.toString())){
            log.info("################ Status {}", status);

            Set<BookingResultDto> bookingsResults = transportBookingService.findWokerBookingByStatus(ShiftStatus.AWAITING_AUTHORIZATION, workerId);
            shiftres.addAll(shiftRepository.findShiftsAuthosizedAndUnpaid(workerId, status, of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList()));
            shiftres.addAll(workerTrainingSessionService.findWorkerBookings(workerId, WorkerTrainingSessionStatus.WAITING_AUTHORIZATION));
            shiftres.addAll(shiftRepository.findShiftsAuthosizedAndUnpaid(workerId,ShiftStatus.BILLED.toString(), of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList()));
            shiftres.addAll(bookingsResults);

            return shiftres;
        }else if(status.equalsIgnoreCase(ShiftStatus.WAITING_APPROVAL.toString())){
            log.info("################ Status {}", status);

            Set<BookingResultDto> bookingsResults = transportBookingService.findWokerBookingByStatus(ShiftStatus.APPLIED, workerId);
            shiftres.addAll(shiftRepository.findShiftsAuthosizedAndUnpaid(workerId, status, of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList()));
            shiftres.addAll(workerTrainingSessionService.findWorkerBookings(workerId, WorkerTrainingSessionStatus.NEW));
            shiftres.addAll(shiftRepository.findShiftsAuthosizedAndUnpaid(workerId,ShiftStatus.BILLED.toString(), of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList()));
            shiftres.addAll(bookingsResults);
            return shiftres;
        }else if(status.equalsIgnoreCase(ShiftStatus.CANCELLED.toString())){
            log.info("################ Status {}, {}", status, ShiftStatus.CANCELLED.toString());
            shiftres.addAll(shiftRepository.findShiftsPagedOtherStatus(workerId, status, of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList()));
            shiftres.addAll(workerTrainingSessionService.findWorkerBookings(workerId, WorkerTrainingSessionStatus.CANCELLED));
            shiftres.addAll(shiftRepository.findShiftsByWorkerStatus(workerId,ShiftStatus.PAID.toString() , of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList()));
            LocalDateTime date;
            date =  LocalDateTime.now().minusDays(30);

            shiftres = shiftres.stream()
                    .filter(shift1 -> shift1.getLastModifiedDate().isAfter(date))
                    .collect(Collectors.toSet());

            return shiftres;
        }else if(status.equalsIgnoreCase(ShiftStatus.BOOKED.toString())){
            log.info("################ Status {}", status);

            Set<BookingResultDto> bookingsResults = transportBookingService.findWokerBookingByStatus(ShiftStatus.BOOKED, workerId);
            shiftres.addAll(shiftRepository.findShiftsAuthosizedAndUnpaid(workerId, status, of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList()));
            shiftres.addAll(workerTrainingSessionService.findWorkerBookings(workerId, WorkerTrainingSessionStatus.BOOKED));
            shiftres.addAll(shiftRepository.findShiftsAuthosizedAndUnpaid(workerId,ShiftStatus.BILLED.toString(), of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList()));
            shiftres.addAll(bookingsResults);
            return shiftres;
        }else if(status.equalsIgnoreCase(ShiftStatus.APPLIED.toString())){
            log.info("################ Status {}, {}", status, ShiftStatus.APPLIED.toString());
            shiftres.addAll(  shiftRepository.findShiftsPagedOtherStatus(workerId, status, of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList()));

            shiftres.addAll(workerTrainingSessionService.findWorkerBookings(workerId, WorkerTrainingSessionStatus.NEW));
//            shiftres.sort(Comparator.comparing(x -> x.getStart()));
            return shiftres;
        }else if(status.equalsIgnoreCase(ShiftStatus.CLOSED.toString())){
            log.info("################ Status {}", status);

            Set<BookingResultDto> bookingsResults = transportBookingService.findWokerBookingByStatus(ShiftStatus.CLOSED, workerId);
            shiftres.addAll(shiftRepository.findShiftsAuthosizedAndUnpaid(workerId, status, of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList()));
            shiftres.addAll(workerTrainingSessionService.findWorkerBookings(workerId, WorkerTrainingSessionStatus.CLOSED));
            shiftres.addAll(shiftRepository.findShiftsAuthosizedAndUnpaid(workerId,ShiftStatus.BILLED.toString(), of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList()));
            shiftres.addAll(bookingsResults);
            return shiftres;
        }else if(status.equalsIgnoreCase(ShiftStatus.REJECTED.toString())){
            log.info("################ Status {}", status);
            shiftres.addAll(shiftRepository.findShiftsAuthosizedAndUnpaid(workerId, status, of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList()));
            shiftres.addAll(workerTrainingSessionService.findWorkerBookings(workerId, WorkerTrainingSessionStatus.REJECTED));
            shiftres.addAll(shiftRepository.findShiftsAuthosizedAndUnpaid(workerId,ShiftStatus.BILLED.toString(), of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList()));
            return shiftres;
        }else {
            log.info("################ Status {}, {}", status, ShiftStatus.NEW.toString());
            return shiftRepository.findShiftsPagedOtherStatus(workerId, status, of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toSet());
        }
    }

    @Override
    public Page<IShiftCompliance> findShiftComplianceIssues(Long agencyId, PageRequest of) {
        return shiftRepository.findShiftComplianceIssues(agencyId, of);
    }


    @Override
    public Set<BookingResultDto> findAllShiftsForWorkerBilling(Long workerId, Long agencyId, PageRequest of) {
        return shiftRepository.findWorkerShiftsForBilling(workerId,  agencyId, of).stream()
                .map(toShiftResultDto::convert)
                .collect(Collectors.toSet());
    }

    @Override
    public List<BookingResultDto> findAllShiftsForMyAgencyPagedByStatus(Long workerId, String status, PageRequest of) {

        Worker worker = workerService.getOne(workerId);
        log.info("I am here with this loads of shift");

        if (status.equalsIgnoreCase(ShiftStatus.NEW.toString())) {
            log.info("################ Status {}, {}", status, ShiftStatus.NEW.toString());
            return shiftRepository.findShiftsPagedStatus(workerId, status, worker.getGender().toString(), Integer.valueOf(worker.getAssignmentCode().getCode()), of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList());
        } else {
            log.info("################ Status {}, {}", status, ShiftStatus.NEW.toString());
            return shiftRepository.findShiftsPagedOtherStatus(workerId, status, of).stream()
                    .map(toShiftResultDto::convert)
                    .collect(Collectors.toList());

        }
    }

    @Override
    public Page<BookingResultDto> findAllShiftsForMyAgencyPaged(Long workerId, PageRequest of) {
        Worker worker = workerService.getOne(workerId);
        try {
            return shiftRepository.findWorkerShifts(workerId, worker.getGender().toString(), Integer.valueOf(Math.toIntExact(worker.getAssignmentCode().getId())), of)
                    .map(toShiftResultDto::convert);
        }catch (RecordNotFoundException recordNotFoundException){
            throw new BusinessValidationException("No applied shifts yet");
        }
    }

    @Override
    public void enableCarPoolingForEligibleShifts() {
        List<Shift> allShifts = shiftRepository.findAll();

        // Group shifts by date, start time, and directorate
        Map<String, List<Shift>> groupedShifts = allShifts.stream()
                .filter(shift -> shift.getStart() != null && shift.getStart().toLocalTime() != null && shift.getDirectorate() != null)
                .distinct()
                .collect(Collectors.groupingBy(shift -> shift.getStart() + "-"
                        + shift.getStart().toLocalTime() + "-"
                        + shift.getDirectorate().getId()));

        // Iterate over grouped shifts and enable car-pooling for groups with more than one shift
        for (Map.Entry<String, List<Shift>> entry : groupedShifts.entrySet()) {
            List<Shift> shiftsGroup = entry.getValue();

            Set<Long> shiftIdsForCarPooling = shiftsGroup.stream()
                    .map(Shift::getId).collect(Collectors.toSet());


            if (shiftsGroup.size() > 1) {  // More than one shift with the same date, time, and directorate
                String groupName = "Shift_" + shiftsGroup.stream()
                        .map(shift -> String.valueOf(shift.getId())) // Convert each ID to a string
                        .collect(Collectors.joining("_"));

//                Create chatGroup for every shift that satisfies carPooling, join chatGroup
                ChatGroup chatGroup = chatGroupService.createChatGroup(groupName, shiftsGroup);
                shiftsGroup.stream()
                        .filter(shift -> shift.getStatus() == ShiftStatus.BOOKED || shift.getStatus() == ShiftStatus.AWAITING_AUTHORIZATION)
                                .map(Shift::getWorker)
                        .forEach(worker-> chatGroupService.joinChatGroup(chatGroup.getId(), worker.getId()));


                shiftsGroup.forEach(shift -> shift.setCarPooling(true));
                shiftsGroup.forEach(shift -> shift.setCarPoolingShiftSet(shiftIdsForCarPooling));
                shiftRepository.saveAll(shiftsGroup);
            }
        }
    }

    @Override
    @Transactional
    public List<ShiftCarPoolingDto> findAllWorkersForShiftCarPooling(Long shiftId) {
//        find booked workers in a shift
        Shift shift = getOne(shiftId);
        if(shift.getCarPooling() == null)
            throw new BusinessValidationException("Car Pooling not enabled for this shift");

        Set<Long> carPoolingShiftIds = shift.getCarPoolingShiftSet();

        List<Worker> workerList = carPoolingShiftIds.stream()
                .map(this::getOne)
                .filter(shift1 -> shift1.getStatus() == ShiftStatus.BOOKED || shift1.getStatus() == ShiftStatus.AWAITING_AUTHORIZATION)
                .map(Shift::getWorker)
                .collect(Collectors.toList());
//        return workerList.stream()
//                .map(worker -> new ShiftCarPoolingDto(worker.getFirstname(),
//                        worker.getLastname(),
//                        new PostCodeMapper().getLatLongFromPostCode(worker.getPostcode()),
//                        worker.getGender().toString()))
//                .distinct()
//                .collect(Collectors.toList());

        return null;

    }

//    public BookingResultDto convert(Shift shift) {
//
//        BookingResultDto bookingResultDto = new BookingResultDto();
//        bookingResultDto.setId(shift.getId());
//        bookingResultDto.setId(shift.getId());
//
//        bookingResultDto.setReleased(shift.getReleased());
//        bookingResultDto.setReleaseDate(shift.getReleaseDate());
//
//        bookingResultDto.setDirectorate(shift.getDirectorate().getCity());
//        Location location = shift.getDirectorate().getLocation();
//        bookingResultDto.setShiftLocation(location.getCity());
//        bookingResultDto.setPostCode(shift.getDirectorate().getPostCode());
//
//        bookingResultDto.setPhoneNumber(shift.getDirectorate().getPhoneNumber());
//        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("dd/MM/yyyy");
//
//        bookingResultDto.setStart(shift.getStart());
//        bookingResultDto.setEnd(shift.getEnd());
//
//        bookingResultDto.setGender(shift.getGender());
//        if(nonNull(shift.getLastAuthorisationReminder()) ) {
//            var hrs = shift.getLastAuthorisationReminder().until(LocalDateTime.now(), ChronoUnit.HOURS);
//            if (hrs<24) bookingResultDto.setLastAuthorisationReminder(hrs);
//        }
//        bookingResultDto.setIsAgencyBilled(shift.getIsAgencyBilled());
//        bookingResultDto.setShiftType(shift.getShiftType().getCity());
//        bookingResultDto.setAssignmentCode(shift.getAssignmentCode().getCode());
//
//        if (shift.getNotes() != null)
//            bookingResultDto.setNotes(shift.getNotes());
//        if (shift.getLastModifiedDate() != null)
//            bookingResultDto.setLastModifiedDate(shift.getLastModifiedDate());
//        if (shift.getShowNoteToAgency() != null)
//            bookingResultDto.setShowNoteToAgency(shift.getShowNoteToAgency());
//        if (shift.getShowNoteToFw() != null)
//            bookingResultDto.setShowNoteToFw(shift.getShowNoteToFw());
//
//        bookingResultDto.setHoursBeforeBroadcasting(shift.getHoursBeforeBroadcasting());
//        bookingResultDto.setShiftStatus(shift.getStatus().toString());
//        if (shift.getAgency() != null)
//            bookingResultDto.setAgency(shift.getAgency().getCity());
//        if (shift.getWorker() != null)
//            bookingResultDto.setWorker(shift.getWorker().getFirstname() + " " + shift.getWorker().getLastname());
//        if (shift.getAuthorizedDate() != null)
//            bookingResultDto.setAuthorizedDate(shift.getAuthorizedDate());
//
//
//
//        if (shift.getBookedDate() != null)
//            bookingResultDto.setBookedDate(shift.getBookedDate());
//
//        bookingResultDto.setCancelledReason(shift.getCancelReason());
//
//
//        bookingResultDto.setQueriedReason(shift.getQueryReason());
//
//        if (shift.getQueriedDate() != null)
//            bookingResultDto.setQueriedDate(shift.getQueriedDate());
//        if (shift.getCancelledDate() != null)
//            bookingResultDto.setCancelledDate(shift.getCancelledDate());
//
//
//             bookingResultDto.setNotes(shift.getNotes());
//
//
//
//            bookingResultDto.setShowNoteToAgency(shift.getShowNoteToAgency());
//
//            bookingResultDto.setShowNoteToFw(shift.getShowNoteToFw());
//
//
//        bookingResultDto.setHoursBeforeBroadcasting(shift.getHoursBeforeBroadcasting());
//
//        bookingResultDto.setRequireApplicationByWorkers(shift.getRequireApplicationByWorkers());
//             bookingResultDto.setShiftStatus(shift.getStatus().toString());
//
//            bookingResultDto.setCreatedBy(shift.getCreatedBy());
//
//
//        if (shift.getAgency() != null) {
//            bookingResultDto.setAgency(shift.getAgency().getCity());
//            bookingResultDto.setAgencyId(shift.getAgency().getId());
//        }
//        bookingResultDto.setBreakTime(shift.getBreakTime());
//        if (shift.getClient() != null)
//            bookingResultDto.setClient(shift.getClient().getCity());
//
//        bookingResultDto.setAppliedStatus(shift.getAppliedStatus());
//        bookingResultDto.setAgencies(shiftRepository.getShiftAgencies(shift.getId()));
//
//        if(shift.getCarPooling() != null){
//            bookingResultDto.setCarPooling(shift.getCarPooling());
//        }
//
//        if(shift.getCarPoolingShiftSet() != null){
//            bookingResultDto.setCarPoolingShiftSet(shift.getCarPoolingShiftSet());
//        }
////        bookingResultDto.setCarPoolingChatGroupName(shiftRepository.getOne(shift.getId()).getShiftChatGroup().getGroupName());
//        return bookingResultDto;
//    }

//   workersEligible for a shift
    List<WorkerShiftDTO> workersEligibleForShift(Long shiftId){
        List<Object[]> results = shiftRepository.workersEligibleForShift(shiftId);
        return results.stream()
                .map(row -> new WorkerShiftDTO(
                        ((Number) row[0]).longValue(),
                        (String) row[1],
                        (String) row[2],
                        ((Number) row[3]).longValue()
                ))
                .collect(Collectors.toList());
    }


    private void sendEligibleShiftNotification(Worker worker, Shift shift){
        if(LocalDateTime.now().isBefore(shift.getStart())) {
            String title = "You are eligible for a Shift!";
            String body = "Hi, " + worker.getFirstname() + " you are eligible for a shift. Check the details below.\n" +
                    "\n" +

                    "Date:" + shift.getStart() + "\n" +
                    "Assignment Code:" + shift.getAssignmentCode().getCode() + "\n" +
                    "Client:" + shift.getClient().getName().toString() + "\n" +
                    "Location:" + shift.getDirectorate().getLocation().getCity() + "\n" +
                    "Directorate:" + shift.getDirectorate().getName() + "\n" +
                    "Start Time:" + shift.getStart().toLocalTime() + "\n" +
                    "End Time:" + shift.getEnd().toLocalTime() +
                    "";

            NotificationCreateDto notificationCreateDto = new NotificationCreateDto();
            notificationCreateDto.setTitle(title);
            notificationCreateDto.setBody(body);
            notificationCreateDto.setWorkerId(worker.getId());
            notificationService.addWorkerNotification(notificationCreateDto);

            worker.getDevices().forEach(d -> {
                CompletableFuture.runAsync(() -> {
                    try {
                        pushNotification.sendPushMessage(title, body, d.getFcmToken());
                    } catch (FirebaseMessagingException e) {
                        log.error(e.toString());
                        deviceService.delete(d);
                    }
                });
            });

        }
    }



    private void sendBookedShiftNotification(Worker worker, Shift shift){
        if(LocalDateTime.now().isBefore(shift.getStart())){
            String title = "You Have been Booked into a Shift!";
            String body =  "Hi, "+worker.getFirstname()+" you have been booked for a shift. Check the details below.\n" +
                    "Shift booked" +
                    "\n" +

                    "Date:"+shift.getStart()+"\n" +
                    "Assignment Code:"+shift.getAssignmentCode().getCode()+"\n" +
                    "Client:"+shift.getClient().getName().toString()+"\n" +
                    "Location:"+shift.getDirectorate().getLocation().getCity()+"\n" +
                    "Directorate:"+shift.getDirectorate().getName()+"\n" +
                    "Start Time:"+shift.getStart().toLocalTime()+"\n" +
                    "End Time:"+shift.getEnd().toLocalTime()+
                    "" ;

            NotificationCreateDto notificationCreateDto = new NotificationCreateDto();
            notificationCreateDto.setTitle(title);
            notificationCreateDto.setBody(body);
            notificationCreateDto.setWorkerId(worker.getId());
            notificationService.addWorkerNotification(notificationCreateDto);

            worker.getDevices().forEach(d->{
                CompletableFuture.runAsync(() -> {
                    try {
                        pushNotification.sendPushMessage(title, body, d.getFcmToken());
                    } catch (FirebaseMessagingException e) {
                        log.error(e.toString());
                        deviceService.delete(d);
                    }
                });
            });
            CompletableFuture.runAsync(() ->
                    emailService.sendSimpleMessage(worker.getEmail(), title, body)
            );
        }
    }


}
