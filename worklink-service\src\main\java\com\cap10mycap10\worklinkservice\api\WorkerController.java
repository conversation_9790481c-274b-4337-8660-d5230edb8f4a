package com.cap10mycap10.worklinkservice.api;


import com.cap10mycap10.worklinkservice.dto.agency.AgencyResultDto;
import com.cap10mycap10.worklinkservice.dto.availability.AvailabilityCreateDto;
import com.cap10mycap10.worklinkservice.dto.availability.IAvailabilityResultDto;
import com.cap10mycap10.worklinkservice.dto.client.ClientDto;
import com.cap10mycap10.worklinkservice.dto.client.IAgency;
import com.cap10mycap10.worklinkservice.dto.device.DeviceWorkerUpdateDto;
import com.cap10mycap10.worklinkservice.dto.note.NoteCreateDto;
import com.cap10mycap10.worklinkservice.dto.notification.NotificationResultDto;
import com.cap10mycap10.worklinkservice.dto.shift.AgencyList;
import com.cap10mycap10.worklinkservice.dto.shift.BookingResultDto;
import com.cap10mycap10.worklinkservice.dto.worker.*;
import com.cap10mycap10.worklinkservice.dto.workercompliance.IWorkerComplianceResultDto;
import com.cap10mycap10.worklinkservice.dto.workercompliance.WorkerComplianceCreateDto;
import com.cap10mycap10.worklinkservice.dto.workercompliance.WorkerComplianceResultDto;
import com.cap10mycap10.worklinkservice.dto.workercompliance.WorkerComplianceUpdateDto;
import com.cap10mycap10.worklinkservice.dto.workertraining.IWorkerTrainingResultDto;
import com.cap10mycap10.worklinkservice.dto.workertraining.WorkerTrainingCreateDto;
import com.cap10mycap10.worklinkservice.dto.workertraining.WorkerTrainingResultDto;
import com.cap10mycap10.worklinkservice.dto.workertraining.WorkerTrainingUpdateDto;
import com.cap10mycap10.worklinkservice.model.Availability;
import com.cap10mycap10.worklinkservice.model.Note;
import com.cap10mycap10.worklinkservice.search.WorkerSearchService;
import com.cap10mycap10.worklinkservice.service.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.io.IOException;
import java.net.URI;
import java.util.List;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;

@Slf4j
@RestController
@RequestMapping(value = "/api/v1/", produces = MediaType.APPLICATION_JSON_VALUE)
public class WorkerController {


    private final WorkerService workerService;
    @Autowired
    private WorkerSearchService workerSearchService;

    private final DeviceService deviceService;

    private final ShiftService shiftService;

    private final NoteService noteService;

    private final NotificationService notificationService;

    private final AvailabilityService availabilityService;

    private final WorkerTrainingService workerTrainingService;

    private final WorkerComplianceService workerComplianceService;


    public WorkerController
            (
                    final WorkerService workerService,
                    DeviceService deviceService, ShiftService shiftService, NoteService noteService,
                    NotificationService notificationService, final AvailabilityService availabilityService,
                    final WorkerTrainingService workerTrainingService,
                    WorkerComplianceService workerComplianceService
            )
    {
        this.workerService = workerService;
        this.deviceService = deviceService;
        this.shiftService = shiftService;
        this.noteService = noteService;
        this.notificationService = notificationService;
        this.availabilityService = availabilityService;
        this.workerTrainingService = workerTrainingService;
        this.workerComplianceService = workerComplianceService;
    }


    /*@CreateWorker*/
    @PostMapping(value = "worker", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> create(@RequestBody WorkerCreateDto workerCreateDto) throws JsonProcessingException {
        log.info("Request to add worker with  : {}", workerCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        workerService.save(workerCreateDto);
        return ResponseEntity.created(uri).build();
    }


    /*@RegisterWorkerDevice For Notifications*/
    @PostMapping(value = "device", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity registerWorkerDevice(@RequestBody DeviceWorkerUpdateDto deviceWorkerUpdateDto) throws JsonProcessingException {
        log.info("Request to add worker device with  : {}", deviceWorkerUpdateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        deviceService.save(deviceWorkerUpdateDto);
        return ResponseEntity.created(uri).build();
    }


    /*@GetWorkerNotifications*/
    @GetMapping(value = "notifications/{workerId}/{page}/{size}")
    public ResponseEntity<List<NotificationResultDto> > getWorkerNotifications(@PathVariable("workerId") Long workerId,
                                                                              @PathVariable("page") int page,
                                                                              @PathVariable("size") int size) throws JsonProcessingException {
        log.info("Request to get worker notifications  : {}", workerId);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.ok(notificationService.findWorkerNotifications(workerId, PageRequest.of(page, size)));
    }


    /*@ViewWorker*/
    @GetMapping(value = "worker/{id}")
    public ResponseEntity<WorkerResultDto> findById(@PathVariable("id") Long id) {
        log.info("Request view add worker with  id: {}", id);
        return ResponseEntity.ok(workerService.findById(id));
    }

    /*@ViewWorker*/
    @GetMapping(value = "workers")
    public ResponseEntity<List<WorkerResultDto>> findById() {
        log.info("Request to view all workers.");
        return ResponseEntity.ok(workerService.findAll());
    }

    /*@ViewWorker*/
    @GetMapping(value = "workers/{page}/{size}")
    public ResponseEntity<Page<WorkerResultDto>> findById(@PathVariable("page") int page,
                                                          @PathVariable("size") int size) {
        log.info("Request to view paged workers with  : {}, {}", page, size);
        return ResponseEntity.ok(workerService.findAllPaged(PageRequest.of(page, size)));
    }

    /*@ViewWorkerDashboard*/
    @GetMapping(value = "worker-dashboard")
    public ResponseEntity<Integer> findNumberOfWorkers() {
        log.info("Request to view worker dashboard");
        return ResponseEntity.ok(workerService.findNumberOfWorkers());
    }

    /*@ViewWorker
    @ViewAgency*/
    @GetMapping(value = "worker-agencies/{workerId}/{page}/{size}")
    public ResponseEntity<Page<AgencyResultDto>> findAllAgencies(@PathVariable("workerId") Long workerId,
                                                         @PathVariable("page") int page,
                                                         @PathVariable("size") int size) {

        log.info("Request to add view paged worker agencies  with  : workerID: {}", workerId);
        return ResponseEntity.ok(workerService.findAllAgenciesPaged(workerId, PageRequest.of(page, size)));
    }

    /*@UpdateWorker*/
    @PutMapping(value = "worker"
//            , consumes = MediaType.APPLICATION_JSON_VALUE
    )
    public ResponseEntity update(@RequestBody WorkerUpdateDto workerUpdateDto
//            ,@RequestParam("image") MultipartFile multipartFile
    ) throws IOException {
        log.info("Request to update worker with  : {}", workerUpdateDto);

//        String fileName = StringUtils.cleanPath(multipartFile.getOriginalFilename());
//        workerUpdateDto.setProfilePic(fileName);
//
//
//        String uploadDir = "user-photos/" + workerUpdateDto.getId();
//
//        FileUploadUtil.saveFile(uploadDir, fileName, multipartFile);


        workerService.save(workerUpdateDto);
        return ResponseEntity.ok().build();
    }


    /* @UpdateWorker*/
//    @PutMapping(value = "worker-agency/{workerId}/{agencyId}")
//    public ResponseEntity<WorkerResultDto> updateAgency(@PathVariable("workerId") Long workerId,
//                                                        @PathVariable("agencyId") Long agencyId) {
//        log.info("Request to update  worker agency  with  :workerID:  {}, agencyId: {}", workerId, agencyId);
//        workerService.updateAgency(workerId, agencyId);
//        return ResponseEntity.ok().build();
//    }



    /*@ViewAgency*/
    @GetMapping(value = "worker-agency/{workerId}/{page}/{size}")
    public ResponseEntity<Page<AgencyResultDto>> getMyAgencies(@PathVariable("workerId") Long workerId,
                                                               @PathVariable("page") int page,
                                                               @PathVariable("size") int size
    ) {
        log.info("Request to get  worker agency  with  :workerID:  {}, page: {}, size: {}", workerId, page, size);
        return ResponseEntity.ok(workerService.getMyAgency(workerId, PageRequest.of(page, size)));
    }


    
    @GetMapping(value = "worker-agency-shift/{workerId}/{page}/{size}")
    public ResponseEntity<Page<BookingResultDto>> findShiftsByClientId(@PathVariable("workerId") Long workerId,
                                                                       @PathVariable("page") int page,
                                                                       @PathVariable("size") int size) {
        log.info("Request to get  worker agency shift  with  : workerID: {}", workerId);
        return ResponseEntity.ok(shiftService.findAllShiftsForMyAgencyPaged(workerId, PageRequest.of(page, size)));
    }


    
    @GetMapping(value = "worker-agency-shift-status/{workerId}/{page}/{size}/{status}")
    public ResponseEntity<Set<BookingResultDto>> findShiftsByWorkerIdAndStatus(@PathVariable("workerId") Long workerId,
                                                                                @PathVariable("page") int page,
                                                                                @PathVariable("size") int size,
                                                                                @PathVariable("status") String status) {
        log.info("Request to get  worker agency shift  with  : workerID: {}", workerId);
        return ResponseEntity.ok(shiftService.findAllShiftsForWorkerIdAndStatus(workerId, PageRequest.of(page, size), status));
    }


    @GetMapping(value = "worker-agency-shift/billing/{workerId}/{agencyId}/{page}/{size}")
    public ResponseEntity<Set<BookingResultDto>> findShiftsByWorkerIdforBilling(@PathVariable("workerId") Long workerId,
                                                                                 @PathVariable("agencyId") Long agencyId,
                                                                                 @PathVariable("page") int page,
                                                                                 @PathVariable("size") int size) {
        log.info("Request to get  worker agency shift  with  : workerId: {}", workerId);
        return ResponseEntity.ok(shiftService.findAllShiftsForWorkerBilling(workerId, agencyId, PageRequest.of(page, size) ));
    }


    @GetMapping(value = "redirect-to-payslip/{payslip}/{workerId}")
    public void findShiftsByWorkerIdAndStatus(@PathVariable("workerId") Long workerId,
                                                                             @PathVariable("payslip") String payslip, HttpServletResponse response) {
        log.info("Request to get  worker agency shift  with  : workerID: {}", workerId);

        String projectUrl = "https://qa-api.myworklink.uk/worklink-api/tina/worklink/worker/"+workerId+"/"+payslip;

        response.setHeader("Location", projectUrl);
        response.setStatus(302);
    }



    @GetMapping(value = "worker-applied-shifts/{workerId}/{page}/{size}")
    public ResponseEntity<List<BookingResultDto>> findAppliedShiftsForWorkerId(@PathVariable("workerId") Long workerId,
                                                                               @PathVariable("page") int page,
                                                                               @PathVariable("size") int size
                                                                                  ) {
        log.info("Request to get  worker applied shifts with  : workerID: {}", workerId);
        return ResponseEntity.ok(shiftService.findAppliedShiftsForWorkerId(workerId, PageRequest.of(page, size)));

    }




    /* @ViewShift*/
   /* @GetMapping(value = "worker-agency-shift/{workerId}/{page}/{size}/{status}")
    public ResponseEntity<Page<BookingResultDto>> findShiftsByClientId(@PathVariable("workerId") Long workerId,
                                                                     @PathVariable("page") int page,
                                                                     @PathVariable("size") int size,
                                                                     @PathVariable("status") String status) {
        log.info("Request to get  worker agency shift  with  : workerID: {}", workerId);
        return ResponseEntity.ok(workerService.findAllShiftsForMyAgencyPagedByStatus(workerId, status, PageRequest.of(page, size)));
    }*/


    /*@DeleteWorker*/
    @DeleteMapping(value = "worker/{id}")
    public ResponseEntity delete(@PathVariable("id") Long id) {
        log.info("Request to delete worker with  id : {}", id);
        workerService.deleteById(id);
        return ResponseEntity.noContent().build();
    }

    /* @ViewWorker*/
    @GetMapping("worker-search/{agencyId}/{page}/{size}")
    public ResponseEntity<Page<WorkerResultDto>> searchAllWorkers(@RequestParam(value = "searchCriteria", required = false) String searchCriteria,
                                                              @PathVariable Long agencyId ,@PathVariable int page, @PathVariable int size) {
        return ResponseEntity.ok(workerSearchService.fuzzySearch(searchCriteria, page, size));
    }

    /* @ViewWorker*/
    @GetMapping("worker-search/agency/{agencyId}/{page}/{size}")
    public ResponseEntity<Page<WorkerResultDto>> searchWorker(@RequestParam(value = "searchCriteria", required = false) String searchCriteria,
                                                              @PathVariable Long agencyId ,@PathVariable int page, @PathVariable int size) {
//        return ResponseEntity.ok(workerSearchService.fuzzySearch(searchCriteria, page, size));
        return ResponseEntity.ok(workerService.search(searchCriteria,agencyId, PageRequest.of(page, size)));

    }
    /* @SearchApplicant*/
    @GetMapping("applicant-search/agency/{agencyId}/{page}/{size}")
    public ResponseEntity<Page<WorkerResultDto>> searchApplicant(@RequestParam(value = "searchCriteria", required = false) String searchCriteria,
                                                              @PathVariable Long agencyId ,@PathVariable int page, @PathVariable int size) {
//        return ResponseEntity.ok(workerSearchService.fuzzySearch(searchCriteria, page, size));
        return ResponseEntity.ok(workerService.searchApplicant(searchCriteria,agencyId, PageRequest.of(page, size)));


    }

    @GetMapping("worker-search/client/{payerId}/{page}/{size}")
    public ResponseEntity<Page<WorkerResultDto>> searchClientWorker(@RequestParam(value = "searchCriteria", required = false) String searchCriteria,
                                                              @PathVariable Long clientId ,@PathVariable int page, @PathVariable int size) {
//        return ResponseEntity.ok(workerSearchService.fuzzySearch(searchCriteria, page, size));
        return ResponseEntity.ok(workerService.searchWorkerClient(searchCriteria,clientId, PageRequest.of(page, size)));


    }

    /*@ViewWorkerDashboard*/
    @GetMapping("worker-stats/{id}")
    public ResponseEntity<WorkerStats> getWorkerStats(@PathVariable("id") Long id) {
        return ResponseEntity.ok(workerService.getStats(id));
    }


    @GetMapping(value = "workers-client/{page}/{size}")
    public ResponseEntity<Page<WorkerResultDto>> findClientWorkers(
            @RequestParam(required = true) Long clientId,
            @RequestParam(required = true) Long assignmentCodeId,
            @RequestParam(required = true) String gender,
            @PathVariable("page") int page,
            @PathVariable("size") int size) {
        log.info("Request to view paged workers with  : {}, {}", page, size);
        return ResponseEntity.ok(workerService.findWorkersForClient(clientId, assignmentCodeId, gender, PageRequest.of(page, size)));
    }

    @PutMapping(value = "worker-link/{workerId}/{agencyId}")
    public ResponseEntity linkAgencyToWorker(@PathVariable("workerId") Long workerId,
                                                        @PathVariable("agencyId") Long agencyId) {
        workerService.linkAgencyWorker(workerId, agencyId);
        return ResponseEntity.ok().build();
    }


    @GetMapping(value = "worker-clients/{workerId}/{page}/{size}")
    public ResponseEntity<Page<ClientDto>> findAllClients(@PathVariable("workerId") Long workerId,
                                                          @PathVariable("page") int page,
                                                          @PathVariable("size") int size) {
        log.info("Request to view agency workers: {}, {}", workerId, page, size);
        return ResponseEntity.ok(workerService.findAllClientsPaged(workerId, PageRequest.of(page, size)));
    }



    @PostMapping("workers-under-agency/{assignmentCodeName}/{gender}")
    public ResponseEntity<List<WorkerResultDto>> findAgencyWorkers(
            @PathVariable("assignmentCodeName") Long assignmentCode,
            @PathVariable("gender") String gender,
            @RequestBody AgencyList agencyList){

       return ResponseEntity.ok(workerService.findWorkersForAgencies(assignmentCode, gender, agencyList));


    }









    @PostMapping(value = "availability", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Availability> updateAvailability(@RequestBody AvailabilityCreateDto availabilityCreateDto) {
        log.info("Request to add availability with : {}", availabilityCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        availabilityService.updateAvailability(availabilityCreateDto);
        return ResponseEntity.created(uri)
                .build();
    }

    /*@ViewServices*/
    @GetMapping(value = "availability/{workerId}/{page}/{size}")
    public ResponseEntity<Page<IAvailabilityResultDto>> findWorkerAvailability
    (
            @PathVariable("workerId") Long workerId,
            @PathVariable("page") int page,
            @PathVariable("size") int size
    ){
        log.info("Request to get worker  availability for worker_id: {}", workerId);
        return ResponseEntity.ok(availabilityService.findWorkerAvailability(workerId, PageRequest.of(page, size)));
    }

    @DeleteMapping(value = "availability/{id}")
    public ResponseEntity deleteAvailabilty(@PathVariable("id") Long id) {
        availabilityService.deleteAvailability(id);
        return ResponseEntity.noContent().build();
    }







    @PostMapping(value = "note", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Note> createNote(@RequestBody NoteCreateDto noteCreateDto) {
        log.info("Request to add note with : {}", noteCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        noteService.addNote(noteCreateDto);
        return ResponseEntity.created(uri)
                .build();
    }

    /*@ViewServices*/
    @GetMapping(value = "note/{workerId}/{page}/{size}")
    public ResponseEntity<Set<Note>> findWorkerNotes
    (
            @PathVariable("workerId") Long workerId,
            @PathVariable("page") int page,
            @PathVariable("size") int size
    ){
        log.info("Request to get worker  notes for worker_id: {}", workerId);
        return ResponseEntity.ok(noteService.findWorkerNotes(workerId, PageRequest.of(page, size)));
    }

    @DeleteMapping(value = "note/{id}")
    public ResponseEntity deleteNote(@PathVariable("id") Long id) {
        noteService.deleteNote(id);
        return ResponseEntity.noContent().build();
    }





    @PostMapping(value = "worker-compliance", consumes = {"multipart/form-data"})
    public ResponseEntity<WorkerComplianceResultDto> createWorkerCompliance(@ModelAttribute WorkerComplianceCreateDto workerComplianceCreateDto) {
        log.info("Request to add workerCompliance with : {}", workerComplianceCreateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        return ResponseEntity.ok( workerComplianceService.addWorkerCompliance(workerComplianceCreateDto));
    }








    @PostMapping(value = "worker-training", consumes = {"multipart/form-data"})
    public ResponseEntity < WorkerTrainingResultDto> createWorkerTraining(@ModelAttribute WorkerTrainingCreateDto workerTrainingCreateDto) {
        log.info("Request to add workerTraining with : {}", workerTrainingCreateDto);
        return ResponseEntity.ok(workerTrainingService.addWorkerTraining(workerTrainingCreateDto));

    }


    @PutMapping(value = "worker-training", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Availability> responseWorkerTraining(@RequestBody WorkerTrainingUpdateDto workerTrainingUpdateDto) {
        log.info("Request to update workerTraining with : {}", workerTrainingUpdateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        workerTrainingService.updateWorkerTraining(workerTrainingUpdateDto);
        return ResponseEntity.created(uri)
                .build();
    }



    /*@ViewServices*/
    @GetMapping(value = "worker-training/{workerId}/{page}/{size}")
    public ResponseEntity<Page<WorkerTrainingResultDto>> findWorkerTrainings
    (
            @PathVariable("workerId") Long workerId,
            @PathVariable("page") int page,
            @PathVariable("size") int size
    ){
        log.info("Request to get worker  workerTraining for worker_id: {}", workerId);
        return ResponseEntity.ok(workerTrainingService.findWorkerTrainings(workerId, PageRequest.of(page, size)));
    }



    @GetMapping(value = "worker-training/{id}")
    public ResponseEntity< WorkerTrainingResultDto> findWorkerTraining
    (
            @PathVariable("id") Long id
    ){
        log.info("Request to get worker  workerTraining for worker_id: {}", id);
        return ResponseEntity.ok(workerTrainingService.findById(id));
    }

    @GetMapping(value = "worker-training/{workerId}/{agencyId}/{page}/{size}")
    public ResponseEntity<List<WorkerTrainingResultDto>> findAgencyWorkerTrainings
    (
            @PathVariable("workerId") Long workerId,
            @PathVariable("agencyId") Long agencyId,
            @PathVariable("page") int page,
            @PathVariable("size") int size
    ){
        log.info("Request to get worker  workerTraining for worker_id: {}", workerId);
        return ResponseEntity.ok(workerTrainingService.findAgencyWorkerTrainings(workerId,agencyId, PageRequest.of(page, size)));
    }

    @DeleteMapping(value = "worker-training/{id}")
    public ResponseEntity deleteWorkerTraining(@PathVariable("id") Long id) {
        workerTrainingService.deleteWorkerTraining(id);
        return ResponseEntity.noContent().build();
    }














    @PutMapping(value = "worker-compliance", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Availability> responseWorkerCompliance(@RequestBody WorkerComplianceUpdateDto workerComplianceUpdateDto) {
        log.info("Request to update workerCompliance with : {}", workerComplianceUpdateDto);
        URI uri = ServletUriComponentsBuilder.fromCurrentRequest().build().toUri();
        workerComplianceService.updateWorkerCompliance(workerComplianceUpdateDto);
        return ResponseEntity.created(uri)
                .build();
    }



    /*@ViewServices*/
    @GetMapping(value = "worker-compliance/{workerId}/{page}/{size}")
    public ResponseEntity<Page<IWorkerComplianceResultDto>> findWorkerCompliances
    (
            @PathVariable("workerId") Long workerId,
            @PathVariable("page") int page,
            @PathVariable("size") int size
    ){
        log.info("Request to get worker  workerCompliance for worker_id: {}", workerId);
        return ResponseEntity.ok(workerComplianceService.findWorkerCompliances(workerId, PageRequest.of(page, size)));
    }


    @GetMapping(value = "worker-compliance/{workerId}/{agencyId}/{page}/{size}")
    public ResponseEntity<List<WorkerComplianceResultDto>> findAgencyWorkerCompliances
            (
                    @PathVariable("workerId") Long workerId,
                    @PathVariable("agencyId") Long agencyId,
                    @PathVariable("page") int page,
                    @PathVariable("size") int size
            ){
        log.info("Request to get worker  workerTraining for worker_id: {}", workerId);
        return ResponseEntity.ok(workerComplianceService.findAgencyWorkerCompliances(workerId,agencyId, PageRequest.of(page, size)));
    }


    @DeleteMapping(value = "worker-compliance/{id}")
    public ResponseEntity deleteWorkerCompliance(@PathVariable("id") Long id) {
        workerComplianceService.deleteWorkerCompliance(id);
        return ResponseEntity.noContent().build();
    }








    @PostMapping("/worker/upload-compliance-doc")
    public ResponseEntity uploadComplianceDoc(@RequestParam("file") MultipartFile file,
                                             @RequestParam("workerId") Long workerId,
                                             @RequestParam("compliance") Long compliance
    ) {

        log.info("Request to add compliance document : {}");
        workerComplianceService.addComplianceDoc( workerId, compliance,file);
        return  ResponseEntity.noContent().build();
    }


    @PostMapping("/worker/upload-training-doc")
    public ResponseEntity uploadTrainingDoc(@RequestParam("file") MultipartFile file,
                                             @RequestParam("workerId") Long workerId,
                                             @RequestParam("training") Long training) {

        log.info("Request to add agency image : {}");
        workerTrainingService.addTrainingDoc( workerId, training,file);
        return  ResponseEntity.noContent().build();
    }

















    @PostMapping(value = "/worker/profile-image",consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity uploadProfileImage(@RequestParam("file") MultipartFile file,
                                             @RequestParam("workerId") Long workerId
    ) {

        log.info("Request to add worker profile image : {}",file.getOriginalFilename());

        workerService.addProfilePic( workerId, file);
        return  ResponseEntity.noContent().build();
    }

}
