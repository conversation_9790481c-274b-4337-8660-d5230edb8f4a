package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.*;
import com.cap10mycap10.worklinkservice.dao.specification.VehicleSpecifications;
import com.cap10mycap10.worklinkservice.dto.VehicleFilterDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleAvailabilityDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleDto;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleSearchDto;
import com.cap10mycap10.worklinkservice.dto.file.FileDto;
import com.cap10mycap10.worklinkservice.dto.promocode.PromotionDto;
import com.cap10mycap10.worklinkservice.enums.AssetStatus;
import com.cap10mycap10.worklinkservice.enums.Operator;
import com.cap10mycap10.worklinkservice.enums.VehicleBookingStatus;
import com.cap10mycap10.worklinkservice.events.email.EmailService;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.helpers.DataBucketUtil;
import com.cap10mycap10.worklinkservice.mapper.asset.agency.VehicleToVehicleDto;
import com.cap10mycap10.worklinkservice.mapper.vehiclefilter.VehicleFilterToVehicleFilterDto;
import com.cap10mycap10.worklinkservice.model.*;
import com.cap10mycap10.worklinkservice.service.*;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.cap10mycap10.worklinkservice.config.AppConfiguration.*;
import static com.cap10mycap10.worklinkservice.service.FileStorageService.generateRandomString;
import static com.cap10mycap10.worklinkservice.service.FileStorageService.getFileExtension;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class VehicleServiceImpl implements VehicleService {
    @Value("${env.companyName}")
    private String companyName;
    @Value("${env.supportEmail}")
    private String supportEmail;
    @Autowired
    private VehicleRepository vehicleRepository;
    @Autowired
    private VehicleRateRepository vehicleRatesRepository;
    @Autowired
    private VehiclePhotoRepository vehiclePhotoRepository;
    @Autowired
    private VehicleInventoryRepository vehicleInventoryRepository;
    @Autowired
    private VehicleDocumentRepository vehicleDocumentRepository;
    @Autowired
    private VehicleToVehicleDto toAssetDto;
    @Autowired
    private EmailService emailService;
    @Autowired
    private AgencyService agencyService;

    @Autowired
    private VehicleFilterToVehicleFilterDto toVehicleFilterDto;
    @Autowired
    private LocationService locationService;

    @Autowired
    private VehicleAvailabilityRepository vehicleAvailabilityRepository;

    @Autowired
    private VehicleBookingRepository vehicleBookingRepository;

    @Autowired
    private DataBucketUtil dataBucketUtil;

    @Autowired
    private VehicleLocationRepository vehicleLocationRepository;

    @Override
    public VehicleDto save(VehicleDto asset) {
        try {
            Vehicle vehicleo = vehicleRepository.findByRegno(asset.getRegno().replaceAll(" ", ""));
//            if(nonNull(vehicleo))
//                throw new BusinessValidationException("This car is already registered. Check registration number");
        }catch (IncorrectResultSizeDataAccessException e){
            throw new BusinessValidationException("This car is already registered. Check registration number");
        }
        Vehicle vehicle = vehicleRepository.save(asset.toAsset());

        // Handle multiple locations - add vehicle locations if provided
        if(nonNull(asset.getLocations())) {
            log.info("Processing location assignments for new vehicle, locations count: {}",
                    asset.getLocations().size());
            updateVehicleLocations(vehicle, asset.getLocationIds());
        }

        return toAssetDto.convert(vehicle, false);
    }

    @Override
    public VehicleDto update(@NotNull VehicleDto vehicleDto, Boolean admin) {
        Vehicle vehicle = getOne(vehicleDto.getId());

        if(nonNull(vehicleDto.getVehicleAddons()))
            sendForAdminReview(vehicle);

        if(List.of(AssetStatus.AWAITING, AssetStatus.REJECTED).contains(vehicle.getStatus()) || admin){
            vehicle.setName(vehicleDto.getName());
            vehicle.setType(vehicleDto.getType());
            vehicle.setModel(vehicleDto.getModel());
            vehicle.setColor(vehicleDto.getColor());
            vehicle.setFuelType(vehicleDto.getFuelType());
            vehicle.setTransmissionType(vehicleDto.getTransmissionType());
            vehicle.setRegno(vehicleDto.getRegno());
            vehicle.setDoors(vehicleDto.getDoors());
            vehicle.setSeats(vehicleDto.getSeats());
            vehicle.setEngineNumber(vehicleDto.getEngineNumber());
            vehicle.setEngineSize(vehicleDto.getEngineSize());
        }

        vehicle.setVehicleAddons(vehicleDto.getVehicleAddons());
        vehicle.setMaxAge(vehicleDto.getMaxAge());
        vehicle.setMinAge(vehicleDto.getMinAge());

        // Handle multiple locations - update vehicle locations if provided
        // Note: We also handle empty locations array to allow clearing all locations
        if(nonNull(vehicleDto.getLocationIds())) {
            log.info("Processing location updates for vehicle ID: {}, locations count: {}",
                    vehicleDto.getId(), vehicleDto.getLocations().size());
            updateVehicleLocations(vehicle, vehicleDto.getLocationIds());
        } else {
            log.info("No location updates provided for vehicle ID: {}", vehicleDto.getId());
        }

        vehicle.setNotes(vehicleDto.getNotes());
        vehicle.setMileage(vehicleDto.getMileage());


        vehicle.setDescription(vehicleDto.getDescription());
        vehicle.setAirConditioning(vehicleDto.getAirConditioning());
        vehicle.setDepositAmt(vehicleDto.getDepositAmt());
        vehicle.setTrackerId(vehicleDto.getTrackerId());

        vehicle.setContactPerson(vehicleDto.getContactPerson());
        vehicle.setContactPhone(vehicleDto.getContactPhone());
        vehicle.setContactAddress(vehicleDto.getContactAddress());
        Vehicle savedVehicle = vehicleRepository.save(vehicle);
        return toAssetDto.convert(savedVehicle,false);
    }




    @Override
    public VehicleDto updateRates(@NotNull VehicleDto vehicleDto) {
        Vehicle vehicle = getOne(vehicleDto.getId());
        vehicle.setVehicleRates(vehicleDto.getVehicleRates());
        vehicle.setHourlyRate(vehicleDto.getHourlyRate());
        vehicle.setMileageRate(vehicleDto.getMileageRate());
        vehicle.setMinHireDays(vehicleDto.getMinHireDays());
        vehicle.setDepositAmt(vehicleDto.getDepositAmt());
        vehicle.setMaxDailyMileage(vehicleDto.getMaxDailyMileage());

        if(vehicleDto.getExcessMileageRate() != vehicle.getExcessMileageRate()) {
            log.info("Excess rate for vehicle: {}, has been changed from: {}, to {}", vehicle.getId(), vehicle.getExcessMileageRate(), vehicleDto.getExcessMileageRate());
            vehicle.setExcessMileageRate(vehicleDto.getExcessMileageRate());
        }

        Vehicle saved = vehicleRepository.save(vehicle);

        return toAssetDto.convert(saved,false);
    }


    @Override
    public VehicleDto findById(Long id) {
        return toAssetDto.convert(getOne(id),false);
    }

    @Override
    public Vehicle getOne(Long id) {
        return vehicleRepository.findById(id).orElseThrow(()-> new RecordNotFoundException("Vehicle was not found id:"+id));

    }

    @Override
    public void addVehicleAvailability(Long id, List<LocalDate> dates) {
        Vehicle vehicle = getOne(id);
        // Find existing availabilities for the supplied dates
        List<VehicleAvailability> availabilitiesToRemove = vehicleAvailabilityRepository
                .findByVehicleIdAndDateIn(id, dates);

//        for(VehicleAvailability availability : availabilitiesToRemove){
//            vehicleAvailabilityRepository.delete(availability);
//        }

        if (!availabilitiesToRemove.isEmpty()) {
            vehicleAvailabilityRepository.deleteAll(availabilitiesToRemove);
        }
    }


    @Override
    public void addVehicleUnAvailability(Long id, List<LocalDate> dates) {
        Vehicle vehicle = getOne(id);


        Set<VehicleAvailability> avals = new HashSet<>();
        dates.forEach(d->{
            VehicleAvailability availability =  new VehicleAvailability();
            availability.setDate(d);
            availability.setVehicle(vehicle);
            avals.add(availability);
        });

        vehicleAvailabilityRepository.saveAll(avals);


    }


    @Override
    public Page<VehicleDto> findByAgencyIdAndTypePaged(
            Long agencyId,
            AssetStatus status,
            String searchQuery,
            PageRequest pageRequest) {

        Long locationId = null;



        return vehicleRepository.findAllByAgency(agencyId, status, searchQuery, locationId, pageRequest)
                .map(v->toAssetDto.convert(v, isNull(agencyId)));
    }


    private void emailVehicleRejected(Vehicle vehicle, String comment) {
        String title = "Car Upload Rejection Notice";
        String body = "Dear "+vehicle.getAgency().getName()+",\n" +
                "\n" +
                "I hope this email finds you well. Thank you for submitting your car for listing on our platform. We appreciate your interest and efforts to offer quality rentals to our customers.\n" +
                "\n" +
                "After carefully reviewing the details of your submission, we regret to inform you that your car upload has been rejected due to the following reason: \n" +
                comment + "\n\n" +
                "We understand this may be disappointing, and we encourage you to review the details provided and consider making the necessary adjustments or improvements. Once the issues have been addressed, you are welcome to resubmit your car for consideration.\n" +
                "\n" +
                "If you have any questions or require further clarification, please do not hesitate to reach out to our support team at "+supportEmail+". We are here to help and ensure a smooth experience for you.\n" +
                "\n" +
                "Thank you for your understanding and cooperation.\n\n";

        CompletableFuture.runAsync(()->
                emailService.sendEmailAsUser(Collections.singletonList(vehicle.getAgency().getEmail()), title, body)
        );
    }


    private void emailVehicleAccepted(Vehicle vehicle) {
        String title = "Your Vehicle Has Been Approved!";
        String body = "Dear " + vehicle.getAgency().getName() + ",\n\n" +
                "We are excited to inform you that your vehicle has been successfully approved and is now listed on our platform for marketing and bookings.\n\n" +
                "Vehicle Details:\n" +
                "Vehicle: " + vehicle.getName()+" "+vehicle.getModel() + "\n" +
                "Listing Date: " + dateTimeFormat.format(LocalDateTime.now()) + "\n\n" +
                "Thank you for choosing our platform to showcase your vehicles. We are committed to helping you reach more customers and grow your business. Should you have any questions or require further assistance, please do not hesitate to contact us.\n\n" +
                "We look forward to a successful partnership!\n\n";

        CompletableFuture.runAsync(()->
                emailService.sendEmailAsUser(Collections.singletonList(vehicle.getAgency().getEmail()), title, body)
        );
    }

    private void emailReviewVehicle(Vehicle vehicle) {
        String title = "Request for Manual Review of Vehicle After Changes";
        String body = "Dear Admin,\n" +
                "\n" +
                "This is an automated message generated by "+companyName+".\n" +
                "\n" +
                "Changes have been made to the following vehicle:\n" +
                "\n" +
                "Vehicle Details:\n" +
                "- Car rental: "+vehicle.getAgency().getName()+"\n" +
                "- Make and Model: "+vehicle.getModel()+" "+vehicle.getName()+"\n" +
                "- Registration Number: "+vehicle.getRegno()+"\n" +
                "\n" +
                "We kindly request that you perform a thorough manual review of the vehicle to ensure all details are accurate and up-to-date in the system.\n" +
                "\n";

        CompletableFuture.runAsync(()->
                emailService.sendEmailAsUser(Collections.singletonList(supportEmail), title, body)
        );
    }

    @Override
    public VehicleDto approveVehicle(Long vehicleId){
        Vehicle vehicle =  getOne(vehicleId);
        vehicle.approve();
        emailVehicleAccepted(vehicle);
        return toAssetDto.convert(vehicleRepository.save(vehicle),false);
    }


    @Override
    public VehicleDto enableVehicle(Long vehicleId){
        Vehicle vehicle =  getOne(vehicleId);
        vehicle.enable();
        return toAssetDto.convert(vehicleRepository.save(vehicle),false);
    }


    @Override
    public VehicleDto disableVehicle(Long vehicleId){
        Vehicle vehicle =  getOne(vehicleId);
        vehicle.disable();
        return toAssetDto.convert(vehicleRepository.save(vehicle),false);
    }

    @Override
    public VehicleDto rejectVehicle(Long vehicleId, String comment){
        Vehicle vehicle =  getOne(vehicleId);
        vehicle.reject();
        emailVehicleRejected(vehicle, comment);
        return toAssetDto.convert(vehicleRepository.save(vehicle),false);
    }

    @Override
    public Page<VehicleDto> findPublicVehicles(VehicleSearchDto vehicleSearchDto, PageRequest of) {
        ZoneId zone;
        if(nonNull(vehicleSearchDto.getLocation())) {
            Location location = locationService.getOne(vehicleSearchDto.getLocation());
            zone = location.getTimeZoneId();
        }
        else zone = ZoneId.of("UTC");

        // Set search dates for promotion filtering if they are provided
        if (nonNull(vehicleSearchDto.getStart()) && nonNull(vehicleSearchDto.getEnd())) {
            ZonedDateTime searchStartDate = vehicleSearchDto.getStart().atZone(zone);
            ZonedDateTime searchEndDate = vehicleSearchDto.getEnd().atZone(zone);
            toAssetDto.setSearchDates(searchStartDate, searchEndDate);
        } else {
            // Clear any previously set search dates
            toAssetDto.setSearchDates(null, null);
        }

        ZonedDateTime startDateTime = null;
        ZonedDateTime endDateTime = null;

        ZonedDateTime searchStartDateTime = null;
        ZonedDateTime searchEndDateTime = null;

        if (nonNull(vehicleSearchDto.getStart()) && nonNull(vehicleSearchDto.getEnd())) {
            startDateTime = vehicleSearchDto.getStart().minusMinutes(59).atZone(zone);
            endDateTime = vehicleSearchDto.getEnd().plusMinutes(59).atZone(zone);

            searchStartDateTime = vehicleSearchDto.getStart().atZone(zone);
            searchEndDateTime = vehicleSearchDto.getEnd().atZone(zone);
        }

        // Set default sorting if not provided
        String sortBy = vehicleSearchDto.getSortBy() != null ? vehicleSearchDto.getSortBy() : "random";
        String sortDirection = vehicleSearchDto.getSortDirection() != null ? vehicleSearchDto.getSortDirection() : "ASC";

        Page<Vehicle> vehicles = vehicleRepository.findPublicVehicles(
                vehicleSearchDto.getVehicleType(),
                startDateTime,
                endDateTime,
                startDateTime.toLocalDate(),
                endDateTime.toLocalDate(),
                searchStartDateTime,
                searchEndDateTime,
                vehicleSearchDto.getLocation(),
                vehicleSearchDto.getAgencyId(),
                vehicleSearchDto.getSearchCriteria(),
                vehicleSearchDto.getTransmission(),
                vehicleSearchDto.getMinPrice(),
                vehicleSearchDto.getMaxPrice(),
                vehicleSearchDto.getMaxDeposit(),
                vehicleSearchDto.getLowMileageLimit(),
                vehicleSearchDto.getHasPromotion(),
                vehicleSearchDto.getPromotionType(),
                sortBy,
                sortDirection,
                of);

        return vehicles.map(v->toAssetDto.convert(v, isNull(vehicleSearchDto.getAgencyId())));

    }



    @Override
    public void addDocument(Long documentId, Long vehicleId, String docName, LocalDate expiryDate, MultipartFile files, LocalDate lastDate , Integer lastMileage,Integer expiryMileage) {

        final Vehicle vehicle = getOne(vehicleId);
        sendForAdminReview(vehicle);

        FileDto fileDto = null;

        if(nonNull(files))
            for(MultipartFile file: List.of(files)){
            String originalFileName = file.getOriginalFilename();
            if(originalFileName == null)
                throw new BusinessValidationException("Original file is null");
            Path path = new File(originalFileName).toPath();

            String extension = getFileExtension(file);
            String name = generateRandomString(10);
            originalFileName=name+"."+extension;

            String contentType = null;
            try {
                contentType = Files.probeContentType(path);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

            fileDto = dataBucketUtil.uploadFile(files, "a/"+ vehicle.getAgency().getId() +"/v/d/"+originalFileName, contentType);


            log.debug("File details successfully saved in the database");

        };



            VehicleDocument ass = new VehicleDocument();

            if(nonNull(documentId))
                ass = vehicleDocumentRepository.getOne(documentId);

            ass.setName(docName);
            ass.setVehicle(vehicle);
            ass.setExpiryDate(expiryDate);
            ass.setLastDate(lastDate);
            ass.setLastMileage(lastMileage);
            ass.setExpiryMileage(expiryMileage);
            if(nonNull(fileDto))ass.setUrl(fileDto.getFileUrl());

            vehicle.getVehicleDocuments().add(ass);
            vehicleRepository.save(vehicle);


//            log.debug("File uploaded successfully, file city: {} and url: {}",fileDto.getFileName(), fileDto.getFileUrl() );
    }

    private void sendForAdminReview(Vehicle vehicle) {
        vehicle.sendForAdminReview();
        emailReviewVehicle(vehicle);
    }

    @Override
    @Transactional
    public void addPhoto(Long vehicleId, List<String> files) {

        final Vehicle vehicle = getOne(vehicleId);
        sendForAdminReview(vehicle);


        for(String file: files) {
            if(file==null) continue;
            var ass = new VehiclePhoto();
            ass.setUrl(file);
            ass.setVehicle(vehicle);
            if (!nonNull(vehicle.getMainPhoto())) ass.setMainPhoto();
            vehicle.getVehiclePhotos().add(ass);
        }

        vehicleRepository.save(vehicle);

        log.debug("File details successfully saved in the database");

    }

    @Override
    public void saveInventory(VehicleInventory inventoryDto) {

        Vehicle vehicle = getOne(inventoryDto.getVehicleId());
        inventoryDto.setVehicle(vehicle);


        VehicleInventory inventory = new VehicleInventory();
        AtomicInteger photoNumber = new AtomicInteger();

        if(nonNull(inventoryDto.getId()))
            inventory = vehicleInventoryRepository.getOne(inventoryDto.getId());
        if(nonNull(inventoryDto.getVehicle().getId())&&!nonNull(inventory.getVehicle())) {
            inventory.setVehicle(vehicle);
//            sendForAdminReview(vehicle);
            vehicleRepository.save(vehicle);
        }
        inventory.setName(inventoryDto.getName());
        inventory.setDateInstalled(inventoryDto.getDateInstalled());
        inventory.setNextCheckDate(inventoryDto.getNextCheckDate());
        inventory.setPrice(inventoryDto.getPrice());
        inventory.setDescription(inventoryDto.getDescription());

        VehicleInventory finalInventory = inventory;
        vehicleInventoryRepository.save(finalInventory);

    }

    @Override
    public void setMainPhoto(Long id) {
        VehiclePhoto photo = vehiclePhotoRepository.findById(id).orElseThrow(() -> new BusinessValidationException("That photo was not found"));
        photo.setMainPhoto();
        photo.getVehicle().sendForAdminReview();
        vehiclePhotoRepository.save(photo);
    }





    @Override
    public Page<VehicleDto> filterVehiclesDto(VehicleFilterDto filter, PageRequest pageRequest) {



        return filterVehicles(toVehicleFilterDto.convertToEntity(filter), pageRequest).map(v->toAssetDto.convert(v, false));
    }

    public Page<Vehicle> filterVehicles(VehicleFilter filter, PageRequest pageRequest) {
        if(isAgency() && filter.getAgencyId()==null) throw new BusinessValidationException("Specify the agency id for this search");

        Specification<Vehicle> specification = Specification.where(null);

        if (filter.getAgencyId() != null) {
            specification = specification.and(VehicleSpecifications.getSpecification("agency", List.of(filter.getAgencyId()), Operator.EQUALITY));
        }

        if (filter.getModels() != null) {
            specification = specification.and(VehicleSpecifications.getSpecification("model", filter.getModels(), filter.getModelOperator()));
        }

        if (filter.getColors() != null) {
            specification = specification.and(VehicleSpecifications.getSpecification("color", filter.getColors(), filter.getColorOperator()));
        }

        if (filter.getTypes() != null) {
            specification = specification.and(VehicleSpecifications.getSpecification("type", filter.getTypes(), filter.getTypeOperator()));
        }

        if (filter.getNames() != null) {
            specification = specification.and(VehicleSpecifications.getSpecification("name", filter.getNames(), filter.getNameOperator()));
        }

        if (filter.getVehicles() != null && !filter.getVehicles().isEmpty()) {
            specification = specification.and(VehicleSpecifications.getSpecification("id", filter.getVehicles().stream().map(Vehicle::getId).collect(Collectors.toList()), filter.getVehicleOperator()));
        }

        if (filter.getFuelTypes() != null) {
            specification = specification.and(VehicleSpecifications.getSpecification("fuelType", filter.getFuelTypes(), filter.getFuelTypeOperator()));
        }

        if (filter.getLocations() != null && !filter.getLocations().isEmpty()) {
            specification = specification.and(VehicleSpecifications.getSpecification("location", List.copyOf(filter.getLocations()), filter.getLocationOperator()));
        }


        return vehicleRepository.findAll(specification, pageRequest);
    }







    @Override
    public void deletePhoto(Long id) {
        vehiclePhotoRepository.deleteById(id);
    }

    @Override
    public Page<VehicleDto> findAllByStatus(AssetStatus type,Long agencyId, PageRequest of) {
        return vehicleRepository.findAllByStatus(type, agencyId, of).map(v->toAssetDto.convert(v, isNull(agencyId)));
    }

    @Override
    public void deleteInventory(Long id) {
        vehicleInventoryRepository.deleteById(id);
    }
    @Override
    public void deleteDocuments(Long id) {
        vehicleDocumentRepository.deleteById(id);
    }

    @Override
    public VehicleAvailabilityDto getVehicleAvailability(Long vehicleId) {
        // Get the vehicle to ensure it exists
        Vehicle vehicle = getOne(vehicleId);

        // Get current date
        LocalDate currentDate = LocalDate.now();

        // Get unavailable dates from VehicleAvailability
        List<LocalDate> unavailableDates = vehicleAvailabilityRepository.findUnavailableDatesByVehicleId(vehicleId, currentDate);

        // Get booked dates from VehicleBooking
        List<VehicleBooking> activeBookings = vehicleBookingRepository.findByVehicleAndStatusIn(
                vehicle,
                List.of(VehicleBookingStatus.BOOKED, VehicleBookingStatus.AUTHORIZED, VehicleBookingStatus.RESERVED)
        );

        // Extract all booked dates from the bookings
        List<LocalDate> bookedDates = activeBookings.stream()
                .flatMap(booking -> booking.getDatesBooked().stream())
                .filter(date -> date.isEqual(currentDate) || date.isAfter(currentDate))
                .distinct()
                .collect(Collectors.toList());

        // Create and return the DTO
        return VehicleAvailabilityDto.builder()
                .vehicleId(vehicleId)
                .unavailableDates(unavailableDates)
                .bookedDates(bookedDates)
                .build();
    }


    @Override
    public VehicleAvailabilityDto clearAllVehicleAvailability(Long vehicleId) {
        // Get the vehicle to ensure it exists
        Vehicle vehicle = getOne(vehicleId);

        // Find and delete all unavailable dates for the vehicle
        List<VehicleAvailability> availabilitiesToRemove = vehicleAvailabilityRepository.findByVehicleId(vehicleId);
        if (!availabilitiesToRemove.isEmpty()) {
            vehicleAvailabilityRepository.deleteAll(availabilitiesToRemove);
        }

        // Get current date
        LocalDate currentDate = LocalDate.now();

        // Get booked dates from VehicleBooking
        List<VehicleBooking> activeBookings = vehicleBookingRepository.findByVehicleAndStatusIn(
                vehicle,
                List.of(VehicleBookingStatus.BOOKED, VehicleBookingStatus.AUTHORIZED, VehicleBookingStatus.RESERVED)
        );

        // Extract all booked dates from the bookings
        List<LocalDate> bookedDates = activeBookings.stream()
                .flatMap(booking -> booking.getDatesBooked().stream())
                .filter(date -> date.isEqual(currentDate) || date.isAfter(currentDate))
                .distinct()
                .collect(Collectors.toList());

        // Create and return the DTO
        return VehicleAvailabilityDto.builder()
                .vehicleId(vehicleId)
                .unavailableDates(List.of()) // Empty list as all unavailable dates have been cleared
                .bookedDates(bookedDates)
                .message("All unavailable dates cleared successfully")
                .build();
    }

    // New methods for handling multiple locations

    @Override
    public void addVehicleLocation(Long vehicleId, Long locationId) {
        addVehicleLocation(vehicleId, locationId, true);
    }

    @Override
    public void addVehicleLocation(Long vehicleId, Long locationId, Boolean active) {
        Vehicle vehicle = getOne(vehicleId);
        Location location = locationService.getOne(locationId);

        // Check if relationship already exists
        Optional<VehicleLocation> existing = vehicleLocationRepository.findByVehicleIdAndLocationId(vehicleId, locationId);
        if (existing.isPresent()) {
            // Update existing relationship
            existing.get().setActive(active);
            vehicleLocationRepository.save(existing.get());
        } else {
            // Create new relationship
            VehicleLocation vehicleLocation = new VehicleLocation(vehicle, location, active);
            vehicleLocationRepository.save(vehicleLocation);
        }
    }

    @Override
    public void removeVehicleLocation(Long vehicleId, Long locationId) {
        vehicleLocationRepository.deleteByVehicleIdAndLocationId(vehicleId, locationId);
    }

    @Override
    public void setVehicleLocationActive(Long vehicleId, Long locationId, Boolean active) {
        Optional<VehicleLocation> vehicleLocation = vehicleLocationRepository.findByVehicleIdAndLocationId(vehicleId, locationId);
        if (vehicleLocation.isPresent()) {
            vehicleLocation.get().setActive(active);
            vehicleLocationRepository.save(vehicleLocation.get());
        } else {
            throw new RecordNotFoundException("Vehicle location relationship not found");
        }
    }

    @Override
    public List<Long> getVehicleLocationIds(Long vehicleId) {
        return vehicleLocationRepository.findByVehicleId(vehicleId)
                .stream()
                .map(vl -> vl.getLocation().getId())
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> getActiveVehicleLocationIds(Long vehicleId) {
        return vehicleLocationRepository.findActiveLocationIdsByVehicleId(vehicleId);
    }

    @Override
    public Page<VehicleDto> findByLocationIds(List<Long> locationIds, Long agencyId, AssetStatus status, PageRequest pageRequest) {
        return vehicleRepository.findByLocationIdsAndAgencyIdAndStatus(locationIds, agencyId, status, pageRequest)
        .map(v->toAssetDto.convert(v, isNull(agencyId)));

    }

    /**
     * Helper method to update vehicle locations
     */
    private void updateVehicleLocations(Vehicle vehicle, Set<Long> newLocations) {
        log.info("Updating vehicle locations for vehicle ID: {}", vehicle.getId());

        // Get current vehicle locations
        List<VehicleLocation> currentVehicleLocations = vehicleLocationRepository.findByVehicleId(vehicle.getId());
        Set<Long> currentLocationIds = currentVehicleLocations.stream()
                .map(vl -> vl.getLocation().getId())
                .collect(Collectors.toSet());

        // Remove locations that are no longer assigned
        Set<Long> locationsToRemove = new HashSet<>(currentLocationIds);
        locationsToRemove.removeAll(newLocations);
        for (Long locationId : locationsToRemove) {
            removeVehicleLocation(vehicle.getId(), locationId);
            // Remove from in-memory collection as well
            vehicle.getVehicleLocations().removeIf(vl -> vl.getLocation().getId().equals(locationId));
        }

        // Add new locations
        Set<Long> locationsToAdd = new HashSet<>(newLocations);
        locationsToAdd.removeAll(currentLocationIds);
        for (Long locationId : locationsToAdd) {
            addVehicleLocation(vehicle.getId(), locationId, true);
        }
    }
}
