package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.AgencyRepository;
import com.cap10mycap10.worklinkservice.dao.AgencyWorkerPropertiesRepository;
import com.cap10mycap10.worklinkservice.dao.TrainingRepository;
import com.cap10mycap10.worklinkservice.dao.WorkerRepository;
import com.cap10mycap10.worklinkservice.dto.InviteWorkerRequestDto;
import com.cap10mycap10.worklinkservice.dto.agencyworkerproperties.AgencyWorkerPropertiesCreateDto;
import com.cap10mycap10.worklinkservice.dto.agencyworkerproperties.IAgencyWorkerProperties;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.AgencyWorkerProperties;
import com.cap10mycap10.worklinkservice.enums.RightToWork;
import com.cap10mycap10.worklinkservice.enums.WorkerStatus;
import com.cap10mycap10.worklinkservice.events.email.EmailService;
import com.cap10mycap10.worklinkservice.service.EmailSenderFactory;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.model.Worker;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.AgencyWorkerPropertiesService;
import com.cap10mycap10.worklinkservice.service.WorkerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Slf4j
@Transactional
public class AgencyWorkerPropertiesServiceImpl implements AgencyWorkerPropertiesService {

    private final AgencyWorkerPropertiesRepository agencyWorkerPropertiesRepository;
    private final AgencyRepository agencyRepository;
    private final EmailService emailService;
    private final EmailSenderFactory emailSenderFactory;

    private final AgencyService agencyService;

    private final WorkerService workerService;
    private final TrainingRepository trainingRepository;

    private final WorkerRepository workerRepository;

    public AgencyWorkerPropertiesServiceImpl(AgencyWorkerPropertiesRepository agencyWorkerPropertiesRepository, AgencyRepository agencyRepository, EmailService emailService, EmailSenderFactory emailSenderFactory, AgencyService agencyService, WorkerService workerService, TrainingRepository trainingRepository, WorkerRepository workerRepository) {
        this.agencyWorkerPropertiesRepository = agencyWorkerPropertiesRepository;
        this.agencyRepository = agencyRepository;
        this.emailService = emailService;
        this.emailSenderFactory = emailSenderFactory;
        this.agencyService = agencyService;
        this.workerService = workerService;
        this.trainingRepository = trainingRepository;
        this.workerRepository = workerRepository;
    }


    @Override
    public AgencyWorkerProperties addAgencyWorkerProperties(AgencyWorkerPropertiesCreateDto agencyWorkerPropertiesCreateDto) {
        IAgencyWorkerProperties iAgencyWorkerProperties = agencyWorkerPropertiesRepository.findAgencyWorkerProperties(
                agencyWorkerPropertiesCreateDto.getWorkerId(), agencyWorkerPropertiesCreateDto.getAgencyId()
        );
        if(iAgencyWorkerProperties==null) {


            AgencyWorkerProperties agencyWorkerProperties = new AgencyWorkerProperties();
            agencyWorkerProperties.setExpiry(agencyWorkerPropertiesCreateDto.getExpiry());
            agencyWorkerProperties.setDbsExpiry(agencyWorkerPropertiesCreateDto.getDbsExpiry());
            agencyWorkerProperties.setDbsNumber(agencyWorkerPropertiesCreateDto.getDbsNumber());
            agencyWorkerProperties.setEmploymentStartDate(agencyWorkerPropertiesCreateDto.getEmploymentStartDate());
            agencyWorkerProperties.setRestrictions(agencyWorkerPropertiesCreateDto.getRestrictions());
            agencyWorkerProperties.setPaymentMethod(agencyWorkerPropertiesCreateDto.getPaymentMethod());
            agencyWorkerProperties.setEmploymentStartDate(agencyWorkerPropertiesCreateDto.getEmploymentStartDate());
            agencyWorkerProperties.setContractEndDate(agencyWorkerPropertiesCreateDto.getContractEndDate());

            agencyWorkerProperties.setEligible(agencyWorkerPropertiesCreateDto.getEligible());
            agencyWorkerProperties.setProof(agencyWorkerPropertiesCreateDto.getProof());
            agencyWorkerProperties.setVisa(agencyWorkerPropertiesCreateDto.getVisa());
            agencyWorkerProperties.setVisaExpiry(agencyWorkerPropertiesCreateDto.getVisaExpiry());
            agencyWorkerProperties.setPaperwork(agencyWorkerPropertiesCreateDto.getPaperwork());
            agencyWorkerProperties.setApprover(agencyWorkerPropertiesCreateDto.getApprover());
            agencyWorkerProperties.setPosition(agencyWorkerPropertiesCreateDto.getPosition());
            agencyWorkerProperties.setComment(agencyWorkerPropertiesCreateDto.getComment());
            agencyWorkerProperties.setSignDate(agencyWorkerPropertiesCreateDto.getSignDate());
            agencyWorkerProperties.setSigned(agencyWorkerPropertiesCreateDto.getSigned());

            agencyWorkerProperties.setPaycycle(agencyWorkerPropertiesCreateDto.getPaycycle());
            agencyWorkerProperties.setWeekHrs(agencyWorkerPropertiesCreateDto.getWeekHrs());
            agencyWorkerProperties.setRtiId(agencyWorkerPropertiesCreateDto.getRtiId());
            agencyWorkerProperties.setStartBasis(agencyWorkerPropertiesCreateDto.getStartBasis());

            agencyWorkerProperties.setRating(agencyWorkerPropertiesCreateDto.getRating());
            agencyWorkerProperties.setRestrictionExpiry(agencyWorkerPropertiesCreateDto.getRestrictionExpiry());
            if(agencyWorkerPropertiesCreateDto.getRightToWork()!=null && agencyWorkerPropertiesCreateDto.getRightToWork().equalsIgnoreCase(RightToWork.RESIDENT.toString())) {
                agencyWorkerProperties.setRightToWork(RightToWork.RESIDENT);
            }
            if(agencyWorkerPropertiesCreateDto.getRightToWork()!=null && agencyWorkerPropertiesCreateDto.getRightToWork().equalsIgnoreCase(RightToWork.VISA.toString())) {
                agencyWorkerProperties.setRightToWork(RightToWork.VISA);
            }


            agencyWorkerProperties.setAgency(agencyService.getOne(agencyWorkerPropertiesCreateDto.getAgencyId()));
            agencyWorkerProperties.setWorker(workerService.getOne(agencyWorkerPropertiesCreateDto.getWorkerId()));
            agencyWorkerPropertiesRepository.save(agencyWorkerProperties);

            return agencyWorkerProperties;
        }else{
            save(agencyWorkerPropertiesCreateDto);
        }
        return null;

    }


    @Override
    public AgencyWorkerProperties activateAgencyWorker(AgencyWorkerPropertiesCreateDto agencyWorkerPropertiesCreateDto) {
        AgencyWorkerProperties agencyWorkerProperties = agencyWorkerPropertiesRepository.findByWorkerIdAndAgencyId(
                agencyWorkerPropertiesCreateDto.getWorkerId(), agencyWorkerPropertiesCreateDto.getAgencyId()
        );
        if(agencyWorkerProperties==null) {
            agencyWorkerProperties = new AgencyWorkerProperties();
            agencyWorkerProperties.setAgency(agencyService.getOne(agencyWorkerPropertiesCreateDto.getAgencyId()));
            agencyWorkerProperties.setWorker(workerService.getOne(agencyWorkerPropertiesCreateDto.getWorkerId()));
        }


        agencyWorkerProperties.setStatus(WorkerStatus.APPROVED);

        agencyWorkerPropertiesRepository.save(agencyWorkerProperties);
        return agencyWorkerProperties;

    }


    @Override
    public AgencyWorkerProperties deactivateAgencyWorker(AgencyWorkerPropertiesCreateDto agencyWorkerPropertiesCreateDto) {
        AgencyWorkerProperties agencyWorkerProperties = agencyWorkerPropertiesRepository.findByWorkerIdAndAgencyId(
                agencyWorkerPropertiesCreateDto.getWorkerId(), agencyWorkerPropertiesCreateDto.getAgencyId()
        );
        if(agencyWorkerProperties==null) {
            agencyWorkerProperties = new AgencyWorkerProperties();
            agencyWorkerProperties.setAgency(agencyService.getOne(agencyWorkerPropertiesCreateDto.getAgencyId()));
            agencyWorkerProperties.setWorker(workerService.getOne(agencyWorkerPropertiesCreateDto.getWorkerId()));
        }


        agencyWorkerProperties.setStatus(WorkerStatus.WAITING);

        agencyWorkerPropertiesRepository.save(agencyWorkerProperties);
        return agencyWorkerProperties;

    }




    @Override
    public AgencyWorkerProperties activateAgencyApplicant(AgencyWorkerPropertiesCreateDto agencyWorkerPropertiesCreateDto) {
        AgencyWorkerProperties agencyWorkerProperties = agencyWorkerPropertiesRepository.findByWorkerIdAndAgencyId(
                agencyWorkerPropertiesCreateDto.getWorkerId(), agencyWorkerPropertiesCreateDto.getAgencyId()
        );
        if(agencyWorkerProperties==null) {
            agencyWorkerProperties = new AgencyWorkerProperties();
            agencyWorkerProperties.setAgency(agencyService.getOne(agencyWorkerPropertiesCreateDto.getAgencyId()));
            agencyWorkerProperties.setWorker(workerService.getOne(agencyWorkerPropertiesCreateDto.getWorkerId()));
        }


        agencyWorkerProperties.setStatus(WorkerStatus.WAITING);

        agencyWorkerPropertiesRepository.save(agencyWorkerProperties);
        return agencyWorkerProperties;

    }


    @Override
    public void deactivateAgencyApplicant(AgencyWorkerPropertiesCreateDto agencyWorkerPropertiesCreateDto) {
        Worker worker = workerService.getOne(agencyWorkerPropertiesCreateDto.getWorkerId());
        Agency agency = agencyService.getOne(agencyWorkerPropertiesCreateDto.getAgencyId());
        agency.getWorkers().remove(worker);
        workerRepository.save(worker);
    }
    @Override
    public void inviteWorker(InviteWorkerRequestDto inviteWorkerRequestDto) {
        Long agencyId = inviteWorkerRequestDto.getAgencyId();

        // Get agency-specific branding if available
        String systemName = "MyWorklink";
        String websiteUrl = "myworklink.uk";

        if (agencyId != null) {
            try {
                EmailSenderFactory.EmailConfiguration config = emailSenderFactory.getEmailConfiguration(agencyId);
                systemName = config.getFromName();
                if (config.getWebsiteUrl() != null && !config.getWebsiteUrl().trim().isEmpty()) {
                    websiteUrl = config.getWebsiteUrl().replace("https://", "").replace("http://", "");
                }
            } catch (Exception e) {
                log.warn("Could not get agency configuration for ID: {}, using default branding", agencyId);
            }
        }

        String title = "You are invited to join " + systemName;
        String body = "You have been invited by "+ inviteWorkerRequestDto.getAgencyName()+" to apply on the " + systemName + " platform." +
                "Use the link below to apply."+"\n" +
                inviteWorkerRequestDto.getLink()+"\n" +
                "For more info visit " + websiteUrl;

        // Use agency ID if available for branded emails
        if (agencyId != null) {
            emailService.sendEmailAsUser(inviteWorkerRequestDto.getEmails(), title, body, agencyId);
        } else {
            emailService.sendEmailAsUser(inviteWorkerRequestDto.getEmails(), title, body);
        }
    }






    @Override
    public void deleteAgencyWorkerProperties(Long id) {
//        AgencyWorkerProperties agencyWorkerProperties = getOne(id);
//        agencyWorkerPropertiesRepository.delete(agencyWorkerProperties);
    }

    @Override
    public AgencyWorkerProperties findById(Long id) {
        return getOne(id);
    }


    @Override
    public IAgencyWorkerProperties findProperties(Long workerId, Long agencyId) {

        IAgencyWorkerProperties properties = agencyWorkerPropertiesRepository.findAgencyWorkerProperties(workerId, agencyId);
        if(properties == null){
            properties = new IAgencyWorkerProperties() {
                @Override
                public Long getId() {
                    return null;
                }

                @Override
                public Long getAgencyId() {
                    return null;
                }

                @Override
                public Long getWorkerId() {
                    return null;
                }

                @Override
                public String getPaymentMethod() {
                    return null;
                }

                @Override
                public String getEmploymentStartDate() {
                    return null;
                }

                @Override
                public String getContractEndDate() {
                    return null;
                }

                @Override
                public String getNextCheckDate() {
                    return null;
                }

                @Override
                public String getTrainingDate() {
                    return null;
                }

                @Override
                public String getTrainingExpiry() {
                    return null;
                }

                @Override
                public String getRightToWork() {
                    return null;
                }

                @Override
                public String getDbsNumber() {
                    return null;
                }

                @Override
                public String getDbsExpiry() {
                    return null;
                }

                @Override
                public String getExpiry() {
                    return null;
                }

                @Override
                public String getRestrictions() {
                    return null;
                }

                @Override
                public String getRestrictionExpiry() {
                    return null;
                }

                @Override
                public Boolean getEligible() {
                    return null;
                }

                @Override
                public String getProof() {
                    return null;
                }

                @Override
                public String getVisa() {
                    return null;
                }

                @Override
                public String getVisaExpiry() {
                    return null;
                }

                @Override
                public String getSignDate() {
                    return null;
                }

                @Override
                public Boolean getPaperwork() {
                    return null;
                }

                @Override
                public String getApprover() {
                    return null;
                }

                @Override
                public String getPosition() {
                    return null;
                }

                @Override
                public String getComment() {
                    return null;
                }

                @Override
                public String getSigned() {
                    return null;
                }

                @Override
                public String getStatus() {
                    return null;
                }
            };
        }
        log.info("Agency worker properties, {}", properties);
        return properties;
    }

    @Override
    public List<AgencyWorkerProperties> findAll() {
        return null;
    }

    @Override
    public Page<AgencyWorkerProperties> findAllPaged(PageRequest of) {
        return null;
    }

    @Override
    public AgencyWorkerProperties save(AgencyWorkerPropertiesCreateDto agencyWorkerPropertiesCreateDto) {
        IAgencyWorkerProperties iAgencyWorkerProperties = agencyWorkerPropertiesRepository.findAgencyWorkerProperties(
                agencyWorkerPropertiesCreateDto.getWorkerId(), agencyWorkerPropertiesCreateDto.getAgencyId()
        );
        if (iAgencyWorkerProperties == null){
            addAgencyWorkerProperties(agencyWorkerPropertiesCreateDto);
        }
        AgencyWorkerProperties agencyWorkerProperties = getOne(iAgencyWorkerProperties.getId());

        if(agencyWorkerPropertiesCreateDto.getExpiry() != null){
            agencyWorkerProperties.setExpiry(agencyWorkerPropertiesCreateDto.getExpiry());
        }
        if(agencyWorkerPropertiesCreateDto.getDbsExpiry() != null){
            agencyWorkerProperties.setDbsExpiry(agencyWorkerPropertiesCreateDto.getDbsExpiry());
        }
        if(agencyWorkerPropertiesCreateDto.getDbsNumber() != null){
            agencyWorkerProperties.setDbsNumber(agencyWorkerPropertiesCreateDto.getDbsNumber());
        }
        if(agencyWorkerPropertiesCreateDto.getEmploymentStartDate() != null){
            agencyWorkerProperties.setEmploymentStartDate(agencyWorkerPropertiesCreateDto.getEmploymentStartDate());
        }
        if(agencyWorkerPropertiesCreateDto.getRestrictions() != null){
            agencyWorkerProperties.setRestrictions(agencyWorkerPropertiesCreateDto.getRestrictions());
        }
        if(agencyWorkerPropertiesCreateDto.getPaymentMethod() != null){
            agencyWorkerProperties.setPaymentMethod(agencyWorkerPropertiesCreateDto.getPaymentMethod());
        }
        if(agencyWorkerPropertiesCreateDto.getEmploymentStartDate() != null){
            agencyWorkerProperties.setEmploymentStartDate(agencyWorkerPropertiesCreateDto.getEmploymentStartDate());
        }
        if(agencyWorkerPropertiesCreateDto.getContractEndDate() != null){
            agencyWorkerProperties.setContractEndDate(agencyWorkerPropertiesCreateDto.getContractEndDate());
        }
        if(agencyWorkerPropertiesCreateDto.getContractEndDate() != null){
            agencyWorkerProperties.setContractEndDate(agencyWorkerPropertiesCreateDto.getContractEndDate());
        }
        if(agencyWorkerPropertiesCreateDto.getNextCheckDate() != null){
            agencyWorkerProperties.setNextCheckDate(agencyWorkerPropertiesCreateDto.getNextCheckDate());
        }
        if(agencyWorkerPropertiesCreateDto.getRating() != null){
            agencyWorkerProperties.setRating(agencyWorkerPropertiesCreateDto.getRating());
        }
        if(agencyWorkerPropertiesCreateDto.getRestrictionExpiry() != null){
            agencyWorkerProperties.setRestrictionExpiry(agencyWorkerPropertiesCreateDto.getRestrictionExpiry());
        }



        if(agencyWorkerPropertiesCreateDto.getEligible() != null){ agencyWorkerProperties.setEligible(agencyWorkerPropertiesCreateDto.getEligible());}
        if(agencyWorkerPropertiesCreateDto.getProof() != null){ agencyWorkerProperties.setProof(agencyWorkerPropertiesCreateDto.getProof());}
        if(agencyWorkerPropertiesCreateDto.getVisa() != null){ agencyWorkerProperties.setVisa(agencyWorkerPropertiesCreateDto.getVisa());}
        if(agencyWorkerPropertiesCreateDto.getVisaExpiry() != null){ agencyWorkerProperties.setVisaExpiry(agencyWorkerPropertiesCreateDto.getVisaExpiry());}
        if(agencyWorkerPropertiesCreateDto.getPaperwork() != null){ agencyWorkerProperties.setPaperwork(agencyWorkerPropertiesCreateDto.getPaperwork());}
        if(agencyWorkerPropertiesCreateDto.getApprover() != null){ agencyWorkerProperties.setApprover(agencyWorkerPropertiesCreateDto.getApprover());}
        if(agencyWorkerPropertiesCreateDto.getPosition() != null){ agencyWorkerProperties.setPosition(agencyWorkerPropertiesCreateDto.getPosition());}
        if(agencyWorkerPropertiesCreateDto.getComment() != null){ agencyWorkerProperties.setComment(agencyWorkerPropertiesCreateDto.getComment());}
        if(agencyWorkerPropertiesCreateDto.getSigned() != null){ agencyWorkerProperties.setSigned(agencyWorkerPropertiesCreateDto.getSigned());}
        if(agencyWorkerPropertiesCreateDto.getSignDate() != null){ agencyWorkerProperties.setSignDate(agencyWorkerPropertiesCreateDto.getSignDate());}

        if(agencyWorkerPropertiesCreateDto.getPaycycle() != null){   agencyWorkerProperties.setPaycycle(agencyWorkerPropertiesCreateDto.getPaycycle());}
        if(agencyWorkerPropertiesCreateDto.getWeekHrs() != null){    agencyWorkerProperties.setWeekHrs(agencyWorkerPropertiesCreateDto.getWeekHrs());}
        if(agencyWorkerPropertiesCreateDto.getRtiId() != null){      agencyWorkerProperties.setRtiId(agencyWorkerPropertiesCreateDto.getRtiId());}
        if(agencyWorkerPropertiesCreateDto.getStartBasis() != null){ agencyWorkerProperties.setStartBasis(agencyWorkerPropertiesCreateDto.getStartBasis());}



        if(agencyWorkerPropertiesCreateDto.getRightToWork()!=null && agencyWorkerPropertiesCreateDto.getRightToWork().equalsIgnoreCase(RightToWork.RESIDENT.toString())) {
            agencyWorkerProperties.setRightToWork(RightToWork.RESIDENT);
        }
        if(agencyWorkerPropertiesCreateDto.getRightToWork()!=null && agencyWorkerPropertiesCreateDto.getRightToWork().equalsIgnoreCase(RightToWork.VISA.toString())) {
            agencyWorkerProperties.setRightToWork(RightToWork.VISA);
        }

        agencyWorkerPropertiesRepository.saveAndFlush(agencyWorkerProperties);

        return agencyWorkerProperties;
    }

    @Override
    public AgencyWorkerProperties getOne(Long id) {
        return agencyWorkerPropertiesRepository.findById(id).orElseThrow(
                () -> new RecordNotFoundException("Properties not found")
        );
    }

    @Override
    public AgencyWorkerProperties convert(IAgencyWorkerProperties agencyWorkerProperties) {

        AgencyWorkerProperties agencyWorkerProperties1 = new AgencyWorkerProperties();
//
//        agencyWorkerProperties1.se
//        shiftResultDto.setId(shift.getId());
//        shiftResultDto.setDirectorate(shiftDirectorateService.getOne(Long.valueOf(shift.getDirectorate())).getCity());
//        Location shiftLocation = shiftDirectorateService.getOne(Long.valueOf(shift.getDirectorate())).getLocation();
//        shiftResultDto.setLocation(shiftLocation.getCity());
//        shiftResultDto.setPostCode(shiftLocation.getPostcode());
//        shiftResultDto.setPhoneNumber(shiftDirectorateService.getOne(Long.valueOf(shift.getDirectorate())).getPhoneNumber());
//        DateTimeFormatter pattern = DateTimeFormatter.ofPattern("dd/MM/yyyy");
//        String string = shift.getStart().format(pattern);
//        shiftResultDto.setStart(string);
//        shiftResultDto.setShiftStartTime(shift.getShiftStartTime());
//        shiftResultDto.setShiftEndTime(shift.getShiftEndTime());
//        shiftResultDto.setGender(shift.getGender());
//        shiftResultDto.setShiftType(shiftTypeService.getOne(shift.getShiftType()).getCity());
//        shiftResultDto.setAssignmentCodeName(assignmentCodeService.getOne(Long.valueOf(shift.getPrimaryCode().toString())).getCity());


        return null;
    }
}
