package com.cap10mycap10.worklinkservice.mapper.transport;

import com.cap10mycap10.worklinkservice.dto.transport.TransportDto;
import com.cap10mycap10.worklinkservice.dto.transport.TransportWorkerSpecDto;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.mapper.asset.agency.VehicleToVehicleDto;
import com.cap10mycap10.worklinkservice.mapper.worker.WorkerToWorkerResultDto;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.Transport;
import com.cap10mycap10.worklinkservice.service.AgencyService;
import com.cap10mycap10.worklinkservice.service.ShiftDirectorateService;
import com.cap10mycap10.worklinkservice.service.LocationService;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;

@Slf4j
@Service
@EqualsAndHashCode
public class TransportToTransportDto implements Function<Transport, TransportDto> {

    @Autowired
    private AgencyService agencyService;
    @Autowired
    private VehicleToVehicleDto toAssetResultDto;
    @Autowired
    private ToTransportingStaffMapper toTransportingStaffMapper;
    @Autowired
    private ShiftDirectorateService shiftDirectorateService;
    @Autowired
    private LocationService locationService;
    @Autowired
    private WorkerToWorkerResultDto toWorkerResultDto;

    @Override
    public TransportDto apply(Transport shift) {

        TransportDto bookingResultDto = new TransportDto();


        if(shift.getId() == null){
            throw new BusinessValidationException("Something wrong extracting shift id...");
        }
        bookingResultDto.setId(shift.getId());

        if(nonNull(shift.getClient())) {
            bookingResultDto.setClientId(shift.getClient().getId());
            bookingResultDto.setClientName(shift.getClient().getName());
        }

        Set<Long> agencyIds = new HashSet<>();
        for(Agency id : shift.getAgencies()){
            agencyIds.add(id.getId());
        }



        if(shift.getAgency() != null){
            bookingResultDto.setAgencyName(shift.getAgency().getName());
            bookingResultDto.setAgencyId(shift.getAgency().getId());
        } else bookingResultDto.setAgencyName("NO AGENCY BOOKED YET");
        bookingResultDto.setTransportLegibleAgencyIds(agencyIds);
        bookingResultDto.setPickupLocationContactNumber(shift.getPickupLocationContactNumber());
        bookingResultDto.setDestinationContactNumber(shift.getDestinationContactNumber());
        bookingResultDto.setDestinationPostCode(shift.getDestinationPostCode());
        bookingResultDto.setDestination(shift.getDestination());
        bookingResultDto.setSelfNeglect(shift.getSelfNeglect());
        bookingResultDto.setRefPrefix(shift.getRef());
        bookingResultDto.setPickupPostCode(shift.getPickupPostCode());
        bookingResultDto.setPickupPostCode(shift.getPickupPostCode());
        bookingResultDto.setPassengerRequiresRestraints(shift.getPassengerRequiresRestraints());
        bookingResultDto.setDateTimeRequired(shift.getDateTimeRequired());

        if(nonNull(shift.getStart()) &&nonNull(shift.getEnd())) bookingResultDto.setTotalMinutes(Duration.between( shift.getStart() ,shift.getEnd()).toMinutes());

        if(shift.getRiskDoc() != null)
            bookingResultDto.setRiskDoc(shift.getRiskDoc());


        bookingResultDto.setPassengerGender(shift.getPassengerGender());
        bookingResultDto.setReasonForTransport(shift.getTransportReason());
        bookingResultDto.setTransportStatus(shift.getTransportStatus());
        bookingResultDto.setOtherRisks(shift.getOtherRisks());
        bookingResultDto.setAssaultStaff(shift.getAssaultStaff());
        bookingResultDto.setCanOfferFood(shift.getCanOfferFood());
        bookingResultDto.setRapidStatus(shift.getRapidStatus());
        bookingResultDto.setPhysicalAggression(shift.getPhysicalAggression());
        bookingResultDto.setBpostCode(shift.getBpostCode());
        bookingResultDto.setBreakTime(shift.getBreakTime());
        bookingResultDto.setWardEscort(shift.getWardEscort());
        bookingResultDto.setRacialIssues(shift.getRacialIssues());
        bookingResultDto.setVerballyAggressive(shift.getVerballyAggressive());
        bookingResultDto.setSelfHarm(shift.getSelfHarm());
        bookingResultDto.setAbsconsionRisk(shift.getAbsconsionRisk());
        bookingResultDto.setIsPassengerAwareOfTransport(shift.getIsPassengerAwareOfTransport());
        bookingResultDto.setSexuallyInappropriate(shift.getSexuallyInappropriate());
        bookingResultDto.setOtherRisks(shift.getOtherRisks());
        bookingResultDto.setStart(shift.getStart());
        bookingResultDto.setEnd(shift.getEnd());


        try {
            JSONObject jsonObj = new JSONObject(shift.getRiskDescriptions());
            bookingResultDto.setAssaultStaffDesc(jsonObj.getString("assaultStaffDesc"));
            bookingResultDto.setSelfHarmDesc(jsonObj.getString("selfHarmDesc"));
            bookingResultDto.setPhysicalAggressionDesc(jsonObj.getString("physicalAggressionDesc"));
            bookingResultDto.setSelfNeglectDesc(jsonObj.getString("selfNeglectDesc"));
            bookingResultDto.setVerballyAggressiveDesc(jsonObj.getString("verballyAggressiveDesc"));
            bookingResultDto.setAbsconsionRiskDesc(jsonObj.getString("absconsionRiskDesc"));
            bookingResultDto.setGenderIssuesDesc(jsonObj.getString("genderIssuesDesc"));
            bookingResultDto.setRacialIssuesDesc(jsonObj.getString("racialIssuesDesc"));
            bookingResultDto.setMentalHealthStatus(jsonObj.getString("mentalHealthStatus"));
        } catch (JSONException e) {
            log.error("Error setting shift risk descriptions, {}",e);
        }





        bookingResultDto.setPatientName(shift.getPatientName());
        bookingResultDto.setReasonsForRestrains(shift.getReasonsForRestrains());
        bookingResultDto.setSpecialRequests(shift.getSpecialRequests());
        bookingResultDto.setCreatedBy(shift.getCreatedBy());
        bookingResultDto.setLastModifiedDate(shift.getLastModifiedDate());
        bookingResultDto.setMileage(shift.getMileage());
        if(nonNull(shift.getVehicle()))bookingResultDto.setVehicle(toAssetResultDto.convert(shift.getVehicle(), false));
        if(nonNull(shift.getTeamLeader()))
          bookingResultDto.setTeamLeader(toWorkerResultDto.convert(shift.getTeamLeader()));

        bookingResultDto.setMha(shift.getMha());
        bookingResultDto.setPcaddress(shift.getPcaddress());
        bookingResultDto.setPcemail(shift.getPcemail());
        bookingResultDto.setPcbusiness(shift.getPcbusiness());
        bookingResultDto.setPward(shift.getPward());
        bookingResultDto.setPname(shift.getPname());
        bookingResultDto.setPdob(shift.getPdob());
        bookingResultDto.setNhs(shift.getNhs());
        bookingResultDto.setDiagnosis(shift.getDiagnosis());
        bookingResultDto.setDname(shift.getDname());
        bookingResultDto.setDbusiness(shift.getDbusiness());
        bookingResultDto.setDward(shift.getDward());
        bookingResultDto.setDcontact(shift.getDcontact());
        bookingResultDto.setDemail(shift.getDemail());
        bookingResultDto.setGenderIssues(shift.getGenderIssues());
        bookingResultDto.setMedication(shift.getMedication());
        bookingResultDto.setPhysicalHealth(shift.getPhysicalHealth());
        bookingResultDto.setRapidTranq(shift.getRapidTranq());
        bookingResultDto.setInfectionControl(shift.getInfectionControl());
        bookingResultDto.setCovid(shift.getCovid());
        bookingResultDto.setOfferFood(shift.getOfferFood());
        bookingResultDto.setAllergies(shift.getAllergies());
        bookingResultDto.setSubmittedBy(shift.getSubmittedBy());
        bookingResultDto.setSemail(shift.getSemail());
        bookingResultDto.setSphone(shift.getSphone());
        bookingResultDto.setPOrderNum(shift.getPOrderNum());
        bookingResultDto.setSbsCode(shift.getSbsCode());
        bookingResultDto.setBname(shift.getBname());
        bookingResultDto.setBaddress(shift.getBaddress());
        bookingResultDto.setBinvoice(shift.getBinvoice());
        bookingResultDto.setBphone(shift.getBphone());
        bookingResultDto.setBemail(shift.getBemail());
        bookingResultDto.setAuthority(shift.getAuthority());
        bookingResultDto.setWalk(shift.getWalk());
        bookingResultDto.setWalkInfo(shift.getWalkInfo());

        bookingResultDto.setCashHandover(shift.getCashHandover());
        bookingResultDto.setPDroppedOff(shift.getPDroppedOff());
        bookingResultDto.setPComment(shift.getPComment());
        bookingResultDto.setPfCleanliness(shift.getPfCleanliness());
        bookingResultDto.setPfCourtesy(shift.getPfCourtesy());
        bookingResultDto.setPfKnowledge(shift.getPfKnowledge());
        bookingResultDto.setPfTreatment(shift.getPfTreatment());
        bookingResultDto.setPfAdvice(shift.getPfAdvice());
        bookingResultDto.setPfComfort(shift.getPfComfort());
        bookingResultDto.setPfExperience(shift.getPfExperience());


        //Team Leader entries
        bookingResultDto.setDropTime(shift.getDropTime());
        bookingResultDto.setPatientRecipient(shift.getPatientRecipient());
        bookingResultDto.setRecipientContact(shift.getRecipientContact());
        bookingResultDto.setRecipientRole(shift.getRecipientRole());
        bookingResultDto.setRecipientSignature(shift.getRecipientSignature());
        bookingResultDto.setNewAddress(shift.getNewAddress());
        bookingResultDto.setNewPostCode(shift.getNewPostCode());
        bookingResultDto.setNewPhone(shift.getNewPhone());
        bookingResultDto.setNewEmail(shift.getNewEmail());

        //Patient feedback
        bookingResultDto.setPDroppedOff(shift.getPDroppedOff());
        bookingResultDto.setPfAdvice(shift.getPfAdvice());
        bookingResultDto.setPfCleanliness(shift.getPfCleanliness());
        bookingResultDto.setPfComfort(shift.getPfComfort());
        bookingResultDto.setPfCourtesy(shift.getPfCourtesy());
        bookingResultDto.setPfExperience(shift.getPfExperience());
        bookingResultDto.setPfKnowledge(shift.getPfKnowledge());





        if(nonNull(shift.getMedicationItems()))bookingResultDto.setMedicationList(List.of(shift.getMedicationItems().split(",")));
        if(nonNull(shift.getPropertyItems()))bookingResultDto.setPropertyList(List.of(shift.getPropertyItems().split(",")));

        if(shift.getPatientDocumentOne() != null){
            bookingResultDto.setPatientDocumentOne(shift.getPatientDocumentOne());
        }
        if(shift.getPatientDocumentTwo() != null){
            bookingResultDto.setPatientDocumentTwo(shift.getPatientDocumentTwo());
        }
        if(shift.getPatientDocumentThree() != null){
            bookingResultDto.setPatientDocumentThree(shift.getPatientDocumentThree());
        }


        if(nonNull(shift.getWorkerSpec())&&shift.getWorkerSpec().size()>0) {
            List<TransportWorkerSpecDto> workerSpec = shift.getWorkerSpec().stream().map(toTransportingStaffMapper).collect(Collectors.toList());
            bookingResultDto.setTransportWorkerSpecList(workerSpec);
        }

        if(nonNull(shift.getVehicleLog()))
            bookingResultDto.setVehicleLog(shift.getVehicleLog().toVehicleLogDto());
        return bookingResultDto;
    }
}
