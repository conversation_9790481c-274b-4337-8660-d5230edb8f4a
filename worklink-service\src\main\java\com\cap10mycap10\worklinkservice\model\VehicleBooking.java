package com.cap10mycap10.worklinkservice.model;

import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleBookingDto;
import com.cap10mycap10.worklinkservice.enums.DamageInfoType;
import com.cap10mycap10.worklinkservice.enums.FrontPlatform;
import com.cap10mycap10.worklinkservice.enums.VehicleBookingStatus;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.cap10mycap10.worklinkservice.model.Invoice.getDatesBetween;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Slf4j
@Setter
public class VehicleBooking extends AbstractAuditingEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String cancelReason;
    private String firstname;
    private String surname;
    private String phone;
    private String email;
    private Boolean byAgency =false;
    @Enumerated(EnumType.STRING)
    private FrontPlatform source;
    private Boolean reminderSent =false;



    private String checkoutItems;
    private String returnItems;
    private byte fuelOut;
    private byte fuelIn;
    private Integer mileageOut;
    private Integer mileageIn;
    private String damageImage;
    private String damageImageIn;
    private ZonedDateTime signatureOutDate;
    private ZonedDateTime signatureInDate;

    // Legacy signature fields - kept for backward compatibility
    // @Deprecated
    // @Column(length = 500)
    // private String  signatureOut;
    // @Deprecated
    // private String  signatureOutName;
    // @Deprecated
    // @Column(length = 500)
    // private String  signatureIn;
    // @Deprecated
    // private String  signatureInName;

    // New signature fields for handover process (signatureOut)
    @Column(length = 500)
    private String signatureOutHirer;
    private String signatureOutHirerName;
    @Column(length = 500)
    private String signatureOutCarRental;
    private String signatureOutCarRentalName;

    // New signature fields for return process (signatureIn)
    @Column(length = 500)
    private String signatureInHirer;
    private String signatureInHirerName;
    @Column(length = 500)
    private String signatureInCarRental;
    private String signatureInCarRentalName;

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "vehicleBooking", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    Set<VehicleBookingPhoto> damagePhotos;

    @OneToOne(cascade = CascadeType.ALL)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Rating rating;

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @OneToMany( mappedBy="vehicleBooking", fetch = FetchType.EAGER)
    private Set<Rating> ratings = new HashSet<>();

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "vehicleBooking", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    private Set<Invoice> invoices = new HashSet<>();

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "vehicleBooking", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    private Set<DamageInfo> damageInfoOut = new HashSet<>();

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "vehicleBooking", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    private Set<DamageInfo> damageInfoIn = new HashSet<>();

    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "vehicleBooking", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    private Set<DamageInfo> extraCharges = new HashSet<>();

    @JsonIgnore
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(nullable = false)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Client client;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(nullable = false)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Vehicle vehicle;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    private Location location;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    private Promotion promotion;


    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private VehicleBookingStatus status = VehicleBookingStatus.RESERVED;

    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @OneToMany(mappedBy = "vehicle", fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    Set<VehicleInventory> vehicleAddons= new HashSet<>();

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    @Column(nullable = false)
    private ZonedDateTime end;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy HH:mm:ss")
    @Column(nullable = false)
    private ZonedDateTime start;

    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @ManyToMany(fetch = FetchType.LAZY, cascade = {
            CascadeType.ALL,
    })
    @JoinTable(name = "vehicle_booking_vehicle_inventory",
            joinColumns = @JoinColumn(name = "vehicle_booking_id"),
            inverseJoinColumns = @JoinColumn(name = "vehicle_inventory_id")
    )
    private Set<VehicleInventory> addons = new HashSet<>();




    public VehicleBooking(VehicleBookingDto vehicleBookingDto, Vehicle vehicle) {

        this.cancelReason = vehicleBookingDto.getCancelReason();
        this.id = vehicleBookingDto.getId();
        this.client = vehicleBookingDto.getClientModel();
        this.firstname = vehicleBookingDto.getFirstname();
        this.surname = vehicleBookingDto.getSurname();
        this.phone = vehicleBookingDto.getPhone();
        this.email = vehicleBookingDto.getEmail();
        this.location = vehicleBookingDto.getLocation();
        this.vehicle = vehicle;

        if(isNull(vehicle.getLocation())) throw new BusinessValidationException("Vehicle location needs to be set for this vehicle");

        this.end = vehicleBookingDto.getEnd().atZone(
                vehicle.getLocation().getTimeZoneId());
        this.start = vehicleBookingDto.getStart().atZone(
                vehicle.getLocation().getTimeZoneId());

        this.byAgency = vehicleBookingDto.getByAgency();
        this.source = vehicleBookingDto.getSource();

        this.status = VehicleBookingStatus.RESERVED;
        this.setVehicleAddons(this.vehicle.getVehicleAddons().stream().filter(a->vehicleBookingDto.getVehicleAddonsIds().contains(a.getId())).collect(Collectors.toSet()));

    }


    public void approveBooking() throws BusinessValidationException{
        this.status = VehicleBookingStatus.BOOKED;
    }

    public void setDamageInfoOut(Set<DamageInfo> damageInfoOut) {
        this.damageInfoOut.clear();
        damageInfoOut.forEach(i-> {
            i.setVehicleBooking(this);
            i.setType(DamageInfoType.OUT);
        });
        this.damageInfoOut.addAll(damageInfoOut);
    }

    public void setDamageInfoIn(Set<DamageInfo> damageInfoIn) {
        this.damageInfoIn.clear();
        damageInfoIn.forEach(i-> {
            i.setVehicleBooking(this);
            i.setType(DamageInfoType.IN);
        });
        this.damageInfoIn.addAll(damageInfoIn);
    }

    public void setExtraCharges(Set<DamageInfo> extraCharges) {
        this.extraCharges.clear();
        extraCharges.forEach(i-> {
            i.setVehicleBooking(this);
            i.setType(DamageInfoType.EXTRA);
        });
        this.extraCharges.addAll(extraCharges);
    }


    public void authorize(ZonedDateTime start, ZonedDateTime end) {
        this.status = VehicleBookingStatus.AUTHORIZED;
    }

    public void cancel() {
        this.status = VehicleBookingStatus.CANCELLED;
    }

    public Invoice generateInvoice() {
        Invoice invoice = new Invoice(this);
        this.invoices.add(invoice);
        return invoice;
    }


    public void setRating(Rating rating) {
        if(nonNull(rating.getRatingItems()))rating.setRating(rating.getRatingItems().stream().mapToInt(RatingItem::getRate).sum()/rating.getRatingItems().size());
        this.rating = rating;
    }

    public LocalDateTime getSignatureInDateZoned() {
        return nonNull(signatureInDate)?signatureInDate.withZoneSameInstant(vehicle.getZone()).toLocalDateTime():null;
    }


    public LocalDateTime getEndZoned() {
        return  nonNull(end)?end.withZoneSameInstant(vehicle.getZone()).toLocalDateTime():null;
    }

    public LocalDateTime getStartZoned() {
        return nonNull(start)?start.withZoneSameInstant(vehicle.getZone()).toLocalDateTime():null;
    }

    public LocalDateTime getSignatureOutDateZoned() {
        return nonNull(signatureOutDate)?signatureOutDate.withZoneSameInstant(vehicle.getZone()).toLocalDateTime():null;
    }

    public Set<DamageInfo> getDamageInfoIn() {
        damageInfoIn = damageInfoIn.stream().filter(d->d.getType()==(DamageInfoType.IN)).collect(Collectors.toSet());
        return damageInfoIn;
    }

    public Set<DamageInfo> getDamageInfoOut() {
        damageInfoOut = damageInfoOut.stream().filter(d->d.getType()==(DamageInfoType.OUT)).collect(Collectors.toSet());
        return damageInfoOut;
    }

    public Set<DamageInfo> getExtraCharges() {
        extraCharges = extraCharges.stream().filter(d->d.getType()==(DamageInfoType.EXTRA)).collect(Collectors.toSet());
        return extraCharges;
    }

    public void setDamagePhotos(Set<VehicleBookingPhoto> damagePhotos) {
        damagePhotos.forEach(d->d.setVehicleBooking(this));
        this.damagePhotos = damagePhotos;
    }

    public void setMileageOut(Integer mileageOut) {
        if(nonNull(this.vehicle.getMileage()) && mileageOut<this.vehicle.getMileage())
            throw new BusinessValidationException("The current mileage is "+this.vehicle.getMileage()+". Cannot set mileage that is less than the current. Go to vehicle and override the mileage to manually set current mileage.");
        this.mileageOut = mileageOut;
        this.vehicle.setMileage(Float.valueOf(mileageOut));
    }

    public void setMileageIn(Integer mileageIn) {
        if(nonNull(this.vehicle.getMileage()) && mileageIn<this.vehicle.getMileage())
            throw new BusinessValidationException("The current mileage is "+this.vehicle.getMileage()+". Cannot set mileage that is less than the current. Go to vehicle and override the mileage to manually set current mileage.");
        this.mileageIn = mileageIn;
        this.vehicle.setMileage(Float.valueOf(mileageIn));
    }

    public List<LocalDate> getDatesBooked() {
        List<LocalDate> dates = getDatesBetween(this.getStartZoned().toLocalDate(), this.getEndZoned().toLocalDate());

        // Remove last date from calculation if 24hrs was not reached
        var start = this.getStartZoned().toLocalTime();
        var end = this.getEndZoned().toLocalTime().minusHours(1).minusMinutes(1);
        if(start.isAfter(end))
            dates.remove(dates.size()-1);

        return dates;
    }

    public void addInvoice(Invoice invoice) {
        invoices.add(invoice);
        invoice.setVehicleBooking(this);
    }



    public int getDaysHired() {
        if (isNull(start) || isNull(end)) {
            return 0;
        }
        var adjusted = end.minusHours(1).minusMinutes(1);
        long hours = java.time.Duration.between(start, adjusted).toHours();

        var res =(int) Math.ceil((double) hours / 24);
        return res;

    }

}


