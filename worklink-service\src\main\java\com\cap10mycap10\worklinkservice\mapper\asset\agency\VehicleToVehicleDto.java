package com.cap10mycap10.worklinkservice.mapper.asset.agency;


import com.cap10mycap10.worklinkservice.dao.TransportRepository;
import com.cap10mycap10.worklinkservice.dao.VehicleBookingRepository;
import com.cap10mycap10.worklinkservice.dao.VehicleLogRepository;
import com.cap10mycap10.worklinkservice.dao.VehicleRepository;
import com.cap10mycap10.worklinkservice.dto.asset.admin.VehicleDto;
import com.cap10mycap10.worklinkservice.dto.promocode.PromotionDto;
import com.cap10mycap10.worklinkservice.enums.RatingType;
import com.cap10mycap10.worklinkservice.enums.VehicleBookingStatus;
import com.cap10mycap10.worklinkservice.mapper.promocode.PromotionToPromotionDto;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.Location;
import com.cap10mycap10.worklinkservice.model.Promotion;
import com.cap10mycap10.worklinkservice.model.Vehicle;
import com.cap10mycap10.worklinkservice.service.PromotionService;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import com.stripe.service.PromotionCodeService;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.TransientPropertyValueException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.cap10mycap10.worklinkservice.config.AppConfiguration.isAdmin;
import static com.cap10mycap10.worklinkservice.config.AppConfiguration.isAgency;
import static java.util.Objects.nonNull;

@Slf4j
@Component
public class VehicleToVehicleDto {

    @Autowired
    private TransportRepository transportRepository;
    @Autowired
    private VehicleBookingRepository vehicleBookingRepository;
    @Autowired
    private VehicleRepository vehicleRepository;
    @Autowired
    private VehicleLogRepository vehicleLogRepository;
    @Autowired
    private PromotionToPromotionDto toPromotionDto;
    @Autowired
    private PromotionService promoCodeService;

    private ZonedDateTime searchStartDate;
    private ZonedDateTime searchEndDate;

    public void setSearchDates(ZonedDateTime start, ZonedDateTime end) {
        this.searchStartDate = start;
        this.searchEndDate = end;
    }

    @Transactional
    public VehicleDto convert(Vehicle vehicle, Boolean fromAdminWebsite) {
        VehicleDto vehicleDto = new VehicleDto();
        vehicleDto.setId( vehicle.getId());
        vehicleDto.setVehicleAddons( vehicle.getVehicleAddons());
        vehicleDto.setMaxAge(vehicle.getMaxAge());
        vehicleDto.setMileage(vehicle.getMileage());
        vehicleDto.setMaxDailyMileage( vehicle.getMaxDailyMileage());
        vehicleDto.setMinAge(vehicle.getMinAge());
        vehicleDto.setName(vehicle.getName());
        vehicleDto.setFuelType(vehicle.getFuelType());
        vehicleDto.setSeats( vehicle.getSeats());
        vehicleDto.setNotes( vehicle.getNotes());
        vehicleDto.setRegno( vehicle.getRegno());
        vehicleDto.setMinHireDays( vehicle.getMinHireDays());
//        vehicleDto.setVehicleAvailabilities( vehicle.getVehicleAvailabilities());
        vehicleDto.setDescription( vehicle.getDescription());
        vehicleDto.setMainPhoto( vehicle.getMainPhoto());
        vehicleDto.setStatus( vehicle.getStatus());
        vehicleDto.setDepositAmt( vehicle.getDepositAmt()  );
        vehicleDto.setType( vehicle.getType());
        if(nonNull(vehicle.getAgency())){ 
            vehicleDto.setAgency( new Agency(vehicle.getAgency().getId(), vehicle.getAgency().getName(), vehicle.getAgency().getLogo()));
            vehicleDto.getAgency().setAddress(vehicle.getAgency().getAddress());
            vehicleDto.getAgency().setEmail(vehicle.getAgency().getEmail());
            vehicleDto.getAgency().setBaseCurrency(vehicle.getAgency().getBaseCurrency());
            vehicleDto.getAgency().setTelephone(vehicle.getAgency().getTelephone());
        }
       
       
        vehicleDto.setModel( vehicle.getModel());
        vehicleDto.setColor( vehicle.getColor());
        vehicleDto.setAirConditioning( vehicle.getAirConditioning());
        vehicleDto.setEngineNumber( vehicle.getEngineNumber());
        vehicleDto.setEngineSize( vehicle.getEngineSize());
        vehicleDto.setEngineSize( vehicle.getEngineSize());
        vehicleDto.setTrackerId( vehicle.getTrackerId());
        vehicleDto.setDoors( vehicle.getDoors());
        vehicleDto.setCapacity( vehicle.getCapacity());
        vehicleDto.setVehicleRates( vehicle.getVehicleRates());
        vehicleDto.setTransmissionType( vehicle.getTransmissionType());
        vehicleDto.setVehicleAddons( vehicle.getVehicleAddons());
        vehicleDto.setContactPerson( vehicle.getContactPerson());
        vehicleDto.setContactPhone( vehicle.getContactPhone());
        vehicleDto.setContactAddress( vehicle.getContactAddress());
        vehicleDto.setHourlyRate( vehicle.getHourlyRate());
        vehicleDto.setMileageRate( vehicle.getMileageRate());

        // Debug logging for locations
        Set<Location> locations = vehicle.getLocations();
        log.info("Vehicle ID: {}, VehicleLocations count: {}, Locations count: {}",
                vehicle.getId(),
                vehicle.getVehicleLocations() != null ? vehicle.getVehicleLocations().size() : 0,
                locations != null ? locations.size() : 0);
        if (locations != null && !locations.isEmpty()) {
            log.info("Vehicle locations: {}", locations.stream()
                    .map(loc -> loc.getCity() + ", " + loc.getCountry())
                    .collect(java.util.stream.Collectors.joining("; ")));
        }
        vehicleDto.setLocations( locations);

        Page<Promotion> promotion;

        if(isAdmin()) {
            promotion = promoCodeService.getLatestPromotions(vehicle, searchStartDate, searchEndDate);
            if(!promotion.getContent().isEmpty())vehicleDto.setPromotions(new HashSet<>(promotion.map(toPromotionDto::convert).getContent()));

        }else if(isAgency()){
            promotion = promoCodeService.getLatestPromotions(vehicle, searchStartDate, searchEndDate);
            if(!promotion.getContent().isEmpty())vehicleDto.setPromotions(new HashSet<>(promotion.map(toPromotionDto::convert).stream().filter(p->!p.getAdminDiscount()).collect(Collectors.toList())));

        }else {
            promotion = promoCodeService.getLatestPublicPromotions(vehicle, searchStartDate, searchEndDate);
            Promotion pro = promotion.stream()
                    .filter(promo -> !promo.getAdminDiscount())
                    .findFirst()
                    .orElse(promotion.isEmpty() ? null : (fromAdminWebsite ? promotion.getContent().get(0) : null));

            var pro1 = new HashSet<PromotionDto>();

            if(pro!=null)
                pro1.add(toPromotionDto.convert(pro));

            if(!promotion.getContent().isEmpty())vehicleDto.setPromotions(pro1);

        }


        vehicleDto.setExcessMileageRate( vehicle.getExcessMileageRate());

        Long vehicleId = vehicle.getId();

        Pageable pageable = PageRequest.of(0, 7);
        try{
            vehicleDto.setRatings(vehicleBookingRepository.findRatingsByVehicleId(vehicleId, RatingType.VEHICLE, pageable).getContent());
            vehicleDto.setRating(vehicleBookingRepository.findAverageRatingByVehicleId(vehicleId, RatingType.VEHICLE));
        }catch (TransientPropertyValueException e){
            log.debug("Booking is not yet saved thus error was thrown and caught: "+ e.getMessage());
        }


        //vehicle.getVehicleDocuments().forEach(AbstractAuditingEntity::getCreatedDate);


        vehicleDto.setDocuments(vehicle.getVehicleDocuments());
        vehicleDto.setPhotos(vehicle.getVehiclePhotos());
        vehicleDto.setInventory(vehicle.getVehicleInventories());

        vehicleDto.setJobCount(transportRepository.countByVehicle(vehicle)+ vehicleBookingRepository.countByVehicleAndStatus(vehicle, VehicleBookingStatus.COMPLETE));
        vehicleLogRepository.findTopByVehicleAndOrderByEndMileageDesc(vehicle.getId()).ifPresent(e-> {
            vehicleDto.setMileage(e.getEndMileage());
        });

        return vehicleDto;
    }
}
