package com.cap10mycap10.worklinkservice.implementation;

import com.cap10mycap10.worklinkservice.dao.*;
import com.cap10mycap10.worklinkservice.dto.notification.NotificationCreateDto;
import com.cap10mycap10.worklinkservice.dto.shift.BookingResultDto;
import com.cap10mycap10.worklinkservice.dto.transportbooking.TransportApplicantsResultDto;
import com.cap10mycap10.worklinkservice.enums.ShiftStatus;
import com.cap10mycap10.worklinkservice.enums.TransportStatus;
import com.cap10mycap10.worklinkservice.events.email.EmailService;
import com.cap10mycap10.worklinkservice.exception.BusinessValidationException;
import com.cap10mycap10.worklinkservice.exception.RecordNotFoundException;
import com.cap10mycap10.worklinkservice.helpers.PushNotification;
import com.cap10mycap10.worklinkservice.mapper.shift.ShiftToShiftResultDto;
import com.cap10mycap10.worklinkservice.model.TransportWorkerSpec;
import com.cap10mycap10.worklinkservice.mapper.transportbooking.TransportBookingToApplicantsResultDtoMapper;
import com.cap10mycap10.worklinkservice.model.*;
import com.cap10mycap10.worklinkservice.service.*;
import com.google.firebase.messaging.FirebaseMessagingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TransportBookingServiceImpl implements TransportBookingService {
    @Autowired
    private TransportService transportService;
    @Autowired
    private EmailService emailService;
    @Autowired
    private TransportWorkerSpecRepository transportWorkerSpecRepository;
    @Autowired
    private VehicleService vehicleService;
    @Autowired
    private ShiftToShiftResultDto toShiftResultDto;
    @Autowired
    private PushNotification pushNotification;
    @Autowired
    private TransportWorkerSpecRepository workerSpecRepository;
    @Autowired
    private TransportBookingToApplicantsResultDtoMapper toApplicantsResultDtoMapper;
    @Autowired
    private WorkerService workerService;
    @Autowired
    private ShiftDirectorateService shiftDirectorateService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private AssignmentCodeRepository assignmentCodeRepository;
    @Autowired
    private ShiftRepository shiftRepository;
    @Autowired
    private ShiftToShiftResultDto toBookingResultDto;
    @Autowired
    private AuthenticationFacadeService authenticationFacadeService;


    @Override
    public BookingResultDto workerApply(Long workerId, Long workerSpecId) {
        Worker worker = workerService.getOne(workerId);
        TransportWorkerSpec workerSPec = getOneWorkerSPec(workerSpecId);
        boolean isAvailable = workerService.getWorkerConflicts(workerId, workerSPec.getTransport().getDateTimeRequired(), workerSPec.getTransport().getDateTimeRequired().plusMinutes(10));

        if(!isAvailable)
            throw new BusinessValidationException("Applicant is not available on this date and time");
        if(workerSPec.getTransport().getAgency() == null)
            throw new BusinessValidationException("No agency booked yet for this transport job: Operation failure.");
        if(!worker.isMyAgency(workerSPec.getTransport().getAgency()))
            throw new BusinessValidationException(String.format("Worker with id: %s does not belong to the agency for this transport request", workerId));

        workerSPec.addApplicant(worker);
        TransportWorkerSpec spec = workerSpecRepository.save(workerSPec);
        return toBookingResultDto.convert(spec.getBookings().stream().filter(e -> Objects.equals(e.getWorker().getId(), worker.getId())).findFirst().get());
    }

    @Override
    public TransportApplicantsResultDto approveBooking(Long transportBookingId) {
        Shift transportBooking = getOne(transportBookingId);
        boolean isAvailable = workerService.getWorkerConflicts(transportBooking.getWorker().getId(), transportBooking.getWorkerSpec().getTransport().getDateTimeRequired(), transportBooking.getWorkerSpec().getTransport().getDateTimeRequired().plusMinutes(10));

        if(!isAvailable)
            throw new BusinessValidationException("Applicant is not available on this date and time");
        transportBooking.approveApplicant();
        return toApplicantsResultDtoMapper.apply(shiftRepository.save(transportBooking));
    }
    @Override
    public void rejectBooking(Long transportBookingId) {
        Shift transportBooking = getOne(transportBookingId);
        transportBooking.getWorkerSpec().removeBooking(transportBooking);
        Transport transport = transportBooking.getWorkerSpec().getTransport();
        if(transportBooking.getStatus().equals(ShiftStatus.APPLIED) )
            notifyApplicationDenied(transportBooking);
        else if(transportBooking.getWorkerNotified())
            notifyWorkerJobCancelled(transportBooking);
        shiftRepository.delete(transportBooking);
        transport.refreshState();
        log.info("Transport booking: {} was deleted by:{} ", transportBooking, authenticationFacadeService.getAuthentication().getName());
    }

    private void notifyApplicationDenied(Shift transportBooking) {
        Worker w = transportBooking.getWorker();
        Transport transport = transportBooking.getWorkerSpec().getTransport();
        String title = "Transport job application denied";
        String body = "Good day "+w.getFirstname()+ "," +
                "\n Your application to transport job "+transport.getRef()+" required on "+transport.getDateTimeRequired()+
                ". Was denied. \n\n";
        CompletableFuture.runAsync(()-> emailService.sendEmailAsUserReply(Collections.singletonList(w.getEmail()), title, body,transport.getAgency().getEmail(),transport.getAgency().getName()));

        CompletableFuture.runAsync(() ->
                w.getDevices().forEach(d->{
                    try {
                        pushNotification.sendPushMessage(title,body,d.getFcmToken());
                    } catch (FirebaseMessagingException e) {
                        log.error("An error occurred while trying to send notification:",e);
                    }
                })
        );
        NotificationCreateDto notificationCreateDto = new NotificationCreateDto(w.getId(), title, body);
        notificationService.addWorkerNotification(notificationCreateDto);
    }

    private void notifyWorkerJobCancelled(Shift transportBooking) {
        Worker w = transportBooking.getWorker();
        Transport transport = transportBooking.getWorkerSpec().getTransport();
        String title = "Transport job booking cancelled";
        String body = "Good day "+w.getFirstname()+ "," +
                "\n Your booking to transport job "+transport.getRef()+" required on "+transport.getDateTimeRequired()+
                "Has been cancelled.\n\n";
        CompletableFuture.runAsync(()-> emailService.sendEmailAsUserReply(Collections.singletonList(w.getEmail()), title, body,transport.getAgency().getEmail(),transport.getAgency().getName()));

        CompletableFuture.runAsync(() ->
                w.getDevices().forEach(d->{
                    try {
                        pushNotification.sendPushMessage(title,body,d.getFcmToken());
                    } catch (FirebaseMessagingException e) {
                        log.error("An error occurred while trying to send notification:",e);
                    }
                })
        );
        NotificationCreateDto notificationCreateDto = new NotificationCreateDto(w.getId(), title, body);
        notificationService.addWorkerNotification(notificationCreateDto);
    }

    @Override
    public Set<BookingResultDto> findWokerBookingByStatus(ShiftStatus bookingStatus, Long workerId) {
        List<Shift> transportBookings = shiftRepository.findAllByWorkerAndStatus(workerService.getOne(workerId), bookingStatus);
        return transportBookings.stream()
                .map(toShiftResultDto::convert)
                .collect(Collectors.toSet());
    }
    @Override
    public Set<BookingResultDto> findNewBookingsForWorker(Long workerId) {
        List<Shift> transportBookings = transportWorkerSpecRepository
                .findNewForWorker(workerId)
                .stream()
                .map(TransportWorkerSpec::getNewBooking)
                .collect(Collectors.toList());

        return transportBookings.stream()
                .map(toShiftResultDto::convert)
                .collect(Collectors.toSet());
    }
    @Override
    public Page<TransportApplicantsResultDto> agencyTransportBookings(Long agencyId, Long transportId, TransportStatus transportStatus, PageRequest of) {
        List<Shift> transportBookings = shiftRepository.findByAgencyId(agencyId);
        if(transportId != null){
            transportBookings = transportBookings.stream()
                    .filter(transportBooking -> transportBooking.getWorkerSpec().getTransport().getId() == transportId)
                    .collect(Collectors.toList());
        }
        if(transportStatus != null){
            transportBookings = transportBookings.stream()
                    .filter(transportBooking -> transportBooking.getStatus().toString().equals(transportStatus.toString()))
                    .collect(Collectors.toList());
        }
        Page<Shift> transportBookingPage = PaginationUtil.paginateTransportBooking(of, transportBookings);
        return transportBookingPage.map(toApplicantsResultDtoMapper);
    }
    @Override
    public BookingResultDto bookWorkerDirectly(Long workerId, Long workerSpecId) {
        TransportWorkerSpec workerSpec = getTransportWorkerSpec(workerSpecId);
        Worker worker =  workerService.getOne(workerId);
        boolean isAvailable = workerService.getWorkerConflicts(workerId, workerSpec.getTransport().getDateTimeRequired(), workerSpec.getTransport().getDateTimeRequired().plusMinutes(10));
        if(!isAvailable)
            throw new BusinessValidationException("Worker is not available on this date and time");

        workerSpec.createBooking(worker);
        workerSpec = workerSpecRepository.save(workerSpec);
        checkWorkerComplianceAndNotify(workerSpec.getTransport().getAgency() ,worker, workerSpec.getTransport());
        Set<Shift> transportBookings = new HashSet<>();
        workerSpec.getTransport().getWorkerSpec().forEach(ws->transportBookings.addAll(ws.getBookings()));
        Optional<Shift> res = transportBookings.stream().filter(e -> e.getWorker().getId().equals(workerId)).findFirst();
        return toBookingResultDto.convert(res.get());
    }


    private void checkWorkerComplianceAndNotify(Agency agency, Worker worker, Transport transport) {
        Boolean compliant = worker.checkCompliance();
        if(!compliant){
            String title = "Compliance issues detected";
            String body = "Good day "+worker.getFirstname()+" "+ worker.getLastname()+".\n" +
                    "Please check detected compliance issues for a recently booked shift: "+transport.getId().toString()+".\n" +
                    "Check the shift details below.\n" +
                    "Job Shift booked\n"+
                    "Check the shift details below.\n" +
                    "Shift booked\n"+
                    "Date:"+transport.getDateTimeRequired()+"\n" +
                    "Location:"+transport.getPcaddress()+"\n";

            CompletableFuture.runAsync(()-> emailService.sendSimpleMessage(worker.getEmail(), title, body));
            CompletableFuture.runAsync(()-> emailService.sendSimpleMessage(agency.getEmail(), title, body));
        }
    }



    private TransportWorkerSpec getTransportWorkerSpec(Long id){
       return workerSpecRepository.findById(id)
               .orElseThrow(
                       ()-> new RecordNotFoundException("Transport job role not found"));
    }
    @Override
    public String authorizeTransportBooking(Long transportBookingId) {
        Shift transportBooking = getOne(transportBookingId);
        if(!transportBooking.getStatus().toString().equals(ShiftStatus.AWAITING_AUTHORIZATION.toString())){
           throw new BusinessValidationException(String.format("You are trying to authorize a booking with a status of : %s", transportBooking.getStatus().toString())) ;
        }
        transportBooking.setStatus(ShiftStatus.AUTHORIZED);
        shiftRepository.save(transportBooking);
        return "Transport Booking successfully authorized";
    }
    @Override
    public TransportApplicantsResultDto cancelTransportBooking(Long transportBookingId, String cancelReason) {
        Shift transportBooking = getOne(transportBookingId);
//        if(LocalDateTime.now().isAfter(transportBooking.getWorkerSpec().getTransport().getDateTimeRequired()))
//            throw new BusinessValidationException("You cannot cancel a booking which has already passed");

        transportBooking.setStatus(ShiftStatus.CANCELLED);
        transportBooking.setCancelReason(cancelReason);
        transportBooking.cancel();

        emailCancelledByWorker(transportBooking);
        shiftRepository.save(transportBooking);
        return toApplicantsResultDtoMapper.apply(transportBooking);
    }

    @Override
    public String authorizationReminder(Long transportBookingId, boolean bool) {
        Shift transportBooking = getOne(transportBookingId);

        if(bool){
            Agency agency = transportBooking.getWorkerSpec().getTransport().getAgency();
            Client client = transportBooking.getWorkerSpec().getTransport().getClient();
            Worker worker = transportBooking.getWorker();

            String title = "Authorization Reminder For a Transport Booking";
            String body = worker.getFirstname() + " " + worker.getLastname() +
                    "Is reminding you to authorize their transport booking .\n";

            CompletableFuture.runAsync(()->
                    emailService.sendEmailAsUserReply(List.of(agency.getEmail(), client.getEmail()), title, body, worker.getEmail(), worker.getEmail())
            );
            return "Authorization Reminder Successfully sent to " + client.getEmail() + " and " + agency.getEmail();
        }
        return "No Reminder Sent";
    }

    private void emailCancelledByWorker(Shift transport) {
        String title = "Secure Transport Job Cancelled";
        String body = "Secure transport job booking "+transport.getWorkerSpec().getTransport().getRef()+" was cancelled by the worker." +
                "Please find a replacement in time.\n";

        CompletableFuture.runAsync(()->
                emailService.sendEmailAsUserReply(Collections.singletonList(transport.getWorkerSpec().getTransport().getAgency().getEmail()), title, body, null, null)
        );
    }

    private Shift getOne(Long transportBookingId) {
        return  shiftRepository.findById(transportBookingId).orElseThrow( ()-> new BusinessValidationException(String.format("No transport booking with id: %s found", transportBookingId)));
    }
    


    private TransportWorkerSpec getOneWorkerSPec(Long id) {
        return workerSpecRepository.findById(id).orElseThrow(()->
                new RecordNotFoundException("Job role not found")
        );
    }
}
