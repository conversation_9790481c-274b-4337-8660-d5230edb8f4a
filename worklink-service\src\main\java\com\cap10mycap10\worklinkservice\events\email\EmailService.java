package com.cap10mycap10.worklinkservice.events.email;


import com.cap10mycap10.worklinkservice.model.Shift;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.multipart.MultipartFile;

import javax.mail.MessagingException;
import java.io.IOException;
import java.util.List;

public interface EmailService {
    // Legacy methods (maintained for backward compatibility)
    void sendMessageWithAttachment(List<String> to, String subject, String text);
    void sendEmail(List<String> to, String subject, String text, MultipartFile file);
    void sendShiftWithAttachment(Shift shift, List<String> agencyEmailAddressList, List<String> workerEmailAddressList);
    void sendInvoice(String to, String subject, String message, String file, Long invoiceId) throws MessagingException, IOException;
    void sendPayAdvice(String to, String subject, String message, String file, Long invoiceId) throws MessagingException, IOException;
    void sendEmailAsUser(List<String> to, String subject, String text);

    @Async("threadPoolTaskExecutor")
    void sendEmailAsUserReply(List<String> to, String subject, String text, String replyToEmail, String replyToName);

    SimpleMailMessage sendSimpleMessage(String to, String subject, String text);

    // New agency-aware methods
    void sendMessageWithAttachment(List<String> to, String subject, String text, Long agencyId);
    void sendEmail(List<String> to, String subject, String text, MultipartFile file, Long agencyId);
    void sendEmailAsUser(List<String> to, String subject, String text, Long agencyId);
    void sendEmailAsUserReply(List<String> to, String subject, String text, String replyToEmail, String replyToName, Long agencyId);
    SimpleMailMessage sendSimpleMessage(String to, String subject, String text, Long agencyId);

    // Agency-specific invoice and pay advice methods
    void sendInvoice(String to, String subject, String message, String file, Long invoiceId, Long agencyId) throws MessagingException, IOException;
    void sendPayAdvice(String to, String subject, String message, String file, Long invoiceId, Long agencyId) throws MessagingException, IOException;
}